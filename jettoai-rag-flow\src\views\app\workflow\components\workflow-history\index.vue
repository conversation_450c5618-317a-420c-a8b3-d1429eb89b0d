<template>
  <a-dropdown
    :visible="open"
    :trigger="['click']"
    position="top"
    :popup-max-height="400"
    @visible-change="handleOpenChange"
  >
    <!-- <a-button type="text" class="history-trigger" @click="handleTrigger">
      <template #icon> -->
    <icon-history @click="handleTrigger" />
    <!-- </template>
    </a-button> -->

    <template #content>
      <div class="history-panel" @click.stop>
        <div class="history-header">
          <div class="header-title">变更历史</div>
          <div class="header-info">当前状态: {{ currentStateIndex + 1 }} / {{ totalStates }}</div>
        </div>

        <div class="history-content">
          <!-- 未来状态 (重做) -->
          <div v-if="futureStates.length > 0" class="history-section">
            <div class="section-title">可重做</div>
            <div class="history-items">
              <div
                v-for="(item, index) in futureStates"
                :key="`future-${index}`"
                :class="['history-item', { 'is-current': item.isCurrent }]"
                @click="() => handleJumpToState(item)"
              >
                <div class="item-icon">
                  <icon-redo />
                </div>
                <div class="item-content">
                  <div class="item-title">{{ getEventLabel(item.event) }}</div>
                  <div class="item-time">{{ formatTime(item.timestamp) }}</div>
                </div>
                <div class="item-step">
                  {{ getStepLabel(item.stepCount) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 当前状态 -->
          <div class="history-section">
            <div class="section-title">当前状态</div>
            <div class="history-items">
              <div class="history-item is-current">
                <div class="item-icon">
                  <icon-check-circle />
                </div>
                <div class="item-content">
                  <div class="item-title">{{ getCurrentStateLabel() }}</div>
                  <div class="item-time">{{ formatTime(Date.now()) }}</div>
                </div>
                <div class="item-step">当前</div>
              </div>
            </div>
          </div>

          <!-- 过去状态 (撤销) -->
          <div v-if="pastStates.length > 0" class="history-section">
            <div class="section-title">可撤销</div>
            <div class="history-items">
              <div
                v-for="(item, index) in pastStates"
                :key="`past-${index}`"
                class="history-item"
                @click="() => handleJumpToState(item)"
              >
                <div class="item-icon">
                  <icon-undo />
                </div>
                <div class="item-content">
                  <div class="item-title">{{ getEventLabel(item.event) }}</div>
                  <div class="item-time">{{ formatTime(item.timestamp) }}</div>
                </div>
                <div class="item-step">
                  {{ getStepLabel(-item.stepCount) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="totalStates === 1" class="empty-state">
            <icon-history class="empty-icon" />
            <div class="empty-text">暂无历史记录</div>
            <div class="empty-desc">开始编辑工作流后将显示变更历史</div>
          </div>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 工作流历史事件类型
enum WorkflowHistoryEvent {
  NodeAdd = 'NodeAdd',
  NodeDelete = 'NodeDelete',
  NodeChange = 'NodeChange',
  NodeMove = 'NodeMove',
  EdgeAdd = 'EdgeAdd',
  EdgeDelete = 'EdgeDelete',
  NoteAdd = 'NoteAdd',
  NoteChange = 'NoteChange',
  NoteDelete = 'NoteDelete',
  LayoutOrganize = 'LayoutOrganize'
}

interface HistoryState {
  event: WorkflowHistoryEvent
  timestamp: number
  stepCount: number
  isCurrent?: boolean
}

interface Props {
  pastStates: HistoryState[]
  futureStates: HistoryState[]
  currentStateIndex: number
}

interface Emits {
  (e: 'jumpToState', state: HistoryState): void
}

const props = withDefaults(defineProps<Props>(), {
  pastStates: () => [],
  futureStates: () => [],
  currentStateIndex: 0
})

const emit = defineEmits<Emits>()

// 响应式数据
const open = ref(false)

// 计算属性
const totalStates = computed(() => {
  return props.pastStates.length + props.futureStates.length + 1
})

// 方法
const handleOpenChange = (visible: boolean) => {
  open.value = visible
}

const handleTrigger = (e: Event) => {
  e.stopPropagation()
  open.value = !open.value
}

const handleJumpToState = (state: HistoryState) => {
  emit('jumpToState', state)
  open.value = false
}

const getEventLabel = (event: WorkflowHistoryEvent) => {
  const labels: Record<WorkflowHistoryEvent, string> = {
    [WorkflowHistoryEvent.NodeAdd]: '添加节点',
    [WorkflowHistoryEvent.NodeDelete]: '删除节点',
    [WorkflowHistoryEvent.NodeChange]: '修改节点',
    [WorkflowHistoryEvent.NodeMove]: '移动节点',
    [WorkflowHistoryEvent.EdgeAdd]: '添加连线',
    [WorkflowHistoryEvent.EdgeDelete]: '删除连线',
    [WorkflowHistoryEvent.NoteAdd]: '添加注释',
    [WorkflowHistoryEvent.NoteChange]: '修改注释',
    [WorkflowHistoryEvent.NoteDelete]: '删除注释',
    [WorkflowHistoryEvent.LayoutOrganize]: '整理布局'
  }
  return labels[event] || '未知操作'
}

const getStepLabel = (stepCount: number) => {
  if (stepCount === 0) return '当前'
  const count = Math.abs(stepCount)
  return stepCount > 0 ? `前进 ${count} 步` : `后退 ${count} 步`
}

const getCurrentStateLabel = () => {
  return '工作流当前状态'
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)} 分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)} 小时前`
  } else {
    return new Date(timestamp).toLocaleDateString()
  }
}
</script>

<style scoped lang="scss">
.history-trigger {
  color: var(--color-text-2);

  &:hover {
    color: var(--color-text-1);
    background-color: var(--color-fill-2);
  }
}

.history-panel {
  width: 300px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--color-border-2);
}

.history-header {
  padding: 16px;
  border-bottom: 1px solid var(--color-border-2);

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-1);
    margin-bottom: 4px;
  }

  .header-info {
    font-size: 12px;
    color: var(--color-text-3);
  }
}

.history-content {
  max-height: 320px;
  overflow-y: auto;
}

.history-section {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-1);

  &:last-child {
    border-bottom: none;
  }
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-3);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-fill-2);
  }

  &.is-current {
    background-color: var(--color-primary-light-1);
    border: 1px solid var(--color-primary-light-3);
    cursor: default;

    .item-icon {
      color: var(--color-primary);
    }

    .item-title {
      color: var(--color-primary);
      font-weight: 500;
    }
  }

  .item-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    color: var(--color-text-3);
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
    min-width: 0;

    .item-title {
      font-size: 14px;
      color: var(--color-text-2);
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-time {
      font-size: 12px;
      color: var(--color-text-4);
    }
  }

  .item-step {
    font-size: 12px;
    color: var(--color-text-3);
    flex-shrink: 0;
    margin-left: 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-text-3);

  .empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .empty-desc {
    font-size: 12px;
    text-align: center;
    line-height: 1.4;
  }
}
</style>
