<template>
  <div class="block-list">
    <div v-if="isEmpty" class="empty-state">
      <icon-search class="empty-icon" />
      <div class="empty-text">未找到匹配的节点</div>
    </div>

    <div v-else class="block-groups">
      <div v-for="(group, classification) in filteredGroups" :key="classification" class="block-group">
        <div v-if="group.length > 0" class="group-header">
          {{ getClassificationName(classification) }}
        </div>

        <div class="group-items">
          <a-tooltip
            v-for="block in group"
            :key="block.type"
            :content="block.description || block.title"
            position="right"
          >
            <div class="block-item" @click="() => handleSelect(block.type)">
              <div class="block-icon" :style="{ backgroundColor: getBlockColor(block.type) }">
                <AiSvgIcon :name="`workflow-${block.type}`" />
              </div>
              <div class="block-title">{{ block.title }}</div>
              <div v-if="block.badge" class="block-badge">
                {{ block.badge }}
              </div>
            </div>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BlockEnum } from '../../types/workflow'
import { nodeColor } from '../../types/workflow'

interface Block {
  type: BlockEnum
  title: string
  description?: string
  classification: string
  badge?: string
}

interface Props {
  searchText: string
  availableBlocks: BlockEnum[]
}

interface Emits {
  (e: 'select', type: BlockEnum): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 节点分类枚举
enum BlockClassification {
  Default = 'default',
  QuestionUnderstand = 'question-understand',
  Logic = 'logic',
  Transform = 'transform',
  Utilities = 'utilities'
}

// 所有可用的节点
const allBlocks: Block[] = [
  // 默认节点
  {
    type: BlockEnum.LLM,
    title: '大模型',
    description: '调用大语言模型进行对话',
    classification: BlockClassification.Default
  },
  {
    type: BlockEnum.KnowledgeRetrieval,
    title: '知识检索',
    description: '从知识库中检索相关信息',
    classification: BlockClassification.Default
  },
  {
    type: BlockEnum.Answer,
    title: '直接回复',
    description: '直接返回预设的回复内容',
    classification: BlockClassification.Default
  },
  {
    type: BlockEnum.End,
    title: '结束',
    description: '工作流结束节点',
    classification: BlockClassification.Default
  },

  // 问题理解
  {
    type: BlockEnum.QuestionClassifier,
    title: '问题分类',
    description: '对用户问题进行分类',
    classification: BlockClassification.QuestionUnderstand
  },

  // 逻辑控制
  {
    type: BlockEnum.IfElse,
    title: '条件分支',
    description: '根据条件执行不同的分支',
    classification: BlockClassification.Logic
  },
  {
    type: BlockEnum.Iteration,
    title: '迭代',
    description: '对列表数据进行迭代处理',
    classification: BlockClassification.Logic
  },
  {
    type: BlockEnum.Loop,
    title: '循环',
    description: '循环执行指定的操作',
    classification: BlockClassification.Logic
  },

  // 数据转换
  {
    type: BlockEnum.Code,
    title: '代码执行',
    description: '执行自定义代码逻辑',
    classification: BlockClassification.Transform
  },
  {
    type: BlockEnum.TemplateTransform,
    title: '模板转换',
    description: '使用模板转换数据格式',
    classification: BlockClassification.Transform
  },
  {
    type: BlockEnum.VariableAggregator,
    title: '变量聚合',
    description: '聚合多个变量的值',
    classification: BlockClassification.Transform
  },
  {
    type: BlockEnum.ParameterExtractor,
    title: '参数提取',
    description: '从文本中提取结构化参数',
    classification: BlockClassification.Transform
  },
  {
    type: BlockEnum.Assigner,
    title: '变量赋值',
    description: '为变量赋值',
    classification: BlockClassification.Transform
  },
  {
    type: BlockEnum.DocExtractor,
    title: '文档提取',
    description: '从文档中提取内容',
    classification: BlockClassification.Transform
  },

  // 工具类
  {
    type: BlockEnum.HttpRequest,
    title: 'HTTP请求',
    description: '发送HTTP请求获取数据',
    classification: BlockClassification.Utilities
  },
  {
    type: BlockEnum.Tool,
    title: '工具',
    description: '调用外部工具',
    classification: BlockClassification.Utilities
  }
]

// 分类顺序
const classificationOrder = [
  BlockClassification.Default,
  BlockClassification.QuestionUnderstand,
  BlockClassification.Logic,
  BlockClassification.Transform,
  BlockClassification.Utilities
]

// 计算过滤后的分组
const filteredGroups = computed(() => {
  const filtered = allBlocks.filter((block) => {
    // 检查是否在可用节点列表中
    if (!props.availableBlocks.includes(block.type)) {
      return false
    }

    // 检查搜索文本
    if (props.searchText) {
      const searchLower = props.searchText.toLowerCase()
      return (
        block.title.toLowerCase().includes(searchLower) ||
        (block.description && block.description.toLowerCase().includes(searchLower))
      )
    }

    return true
  })

  // 按分类分组
  const groups: Record<string, Block[]> = {}
  classificationOrder.forEach((classification) => {
    groups[classification] = filtered.filter((block) => block.classification === classification)
  })

  return groups
})

// 检查是否为空
const isEmpty = computed(() => {
  return Object.values(filteredGroups.value).every((group) => group.length === 0)
})

// 获取分类名称
const getClassificationName = (classification: string) => {
  const names: Record<string, string> = {
    [BlockClassification.Default]: '基础节点',
    [BlockClassification.QuestionUnderstand]: '问题理解',
    [BlockClassification.Logic]: '逻辑控制',
    [BlockClassification.Transform]: '数据转换',
    [BlockClassification.Utilities]: '工具类'
  }
  return names[classification] || classification
}

// 获取节点颜色
const getBlockColor = (type: BlockEnum) => {
  return nodeColor[type] || '#6366f1'
}

// 处理选择
const handleSelect = (type: BlockEnum) => {
  emit('select', type)
}
</script>

<style scoped lang="scss">
.block-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-text-3);

  .empty-icon {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
  }
}

.block-group {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-3);
  margin-bottom: 8px;
  padding: 0 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.group-items {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.block-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-fill-2);
    transform: translateY(-1px);
  }

  .block-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 14px;
  }

  .block-title {
    flex: 1;
    font-size: 14px;
    color: var(--color-text-2);
    font-weight: 500;
  }

  .block-badge {
    font-size: 12px;
    padding: 2px 6px;
    background-color: var(--color-fill-3);
    color: var(--color-text-3);
    border-radius: 4px;
    margin-left: 8px;
  }
}
</style>
