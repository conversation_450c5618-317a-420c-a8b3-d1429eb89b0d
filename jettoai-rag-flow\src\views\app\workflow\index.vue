<template>
  <div id="parentNode" style="width: 100%; height: 100%; position: relative; overflow: hidden">
    <!-- <a-button @click="save">保存</a-button> -->
    <VueFlow
      :id="props.flowId"
      v-model:nodes="nodesData"
      v-model:edges="edgesData"
      :apply-default="false"
      class="custom-vue-flow"
      :initial-zoom="0.7"
      :default-viewport="{ zoom: 0.7 }"
      :min-zoom="0.3"
      elevate-edges-on-select
      :connection-mode="'strict' as ConnectionMode.Strict"
      @viewport-change="onViewportChange"
      @node-click="onNodesClick"
      @connect="handleConnect"
      @click="flowclick"
    >
      <template #node-start="StartNodeProps">
        <StartNode v-bind="StartNodeProps" />
      </template>
      <template #node-custom="customNodeProps">
        <CustomNode v-bind="customNodeProps" />
      </template>
      <template #node-iteration-start="iterationStartNodeProps">
        <IterationiStartNode v-bind="iterationStartNodeProps" />
      </template>
      <template #node-iteration="iterationNodeProps">
        <IterationNode v-bind="iterationNodeProps" />
      </template>
      <template #node-http-request="httpRequestNodeProps">
        <HttpRequestNode v-bind="httpRequestNodeProps" />
      </template>
      <template #node-knowledge-retrieval="KnowledgRetrievalNodeProps">
        <KnowledgRetrievalNode v-bind="KnowledgRetrievalNodeProps" />
      </template>
      <template #node-end="EndNodeProps">
        <EndNode v-bind="EndNodeProps" />
      </template>
      <template #node-llm="llmNodeProps">
        <LlmNode v-bind="llmNodeProps" />
      </template>
      <template #node-answer="answerNodeProps">
        <AnswerNode v-bind="answerNodeProps" />
      </template>
      <template #node-variable-aggregator="variableNodeProps">
        <VariableAggregatorNode v-bind="variableNodeProps" />
      </template>
      <template #node-code="codeNodeProps">
        <CodeNode v-bind="codeNodeProps" />
      </template>
      <template #edge-custom="CustomEdgeProps">
        <CustomEdge v-bind="CustomEdgeProps" />
      </template>
      <template #node-assigner="AssignerNodeProps">
        <AssignerNode v-bind="AssignerNodeProps" />
      </template>
      <template #node-document-extractor="DocumenextractorNodeProps">
        <DocumentextractorNode v-bind="DocumenextractorNodeProps" />
      </template>
      <template #node-if-else="IfElseNodeProps">
        <IfElseNode v-bind="IfElseNodeProps" />
      </template>
      <template #node-parameter-extractor="ParameterExtractorNodeProps">
        <ParameterExtractorNode v-bind="ParameterExtractorNodeProps" />
      </template>
      <template #node-question-classifier="QuestionClassifierNodeProps">
        <QuestionClassifierNode v-bind="QuestionClassifierNodeProps" />
      </template>
      <template #node-variable-assigner="variableAssignerExtractorNodeProps">
        <variableAssignerExtractorNode v-bind="variableAssignerExtractorNodeProps" />
      </template>
      <template #node-tool="ToolNodeProps">
        <ToolNode v-bind="ToolNodeProps" />
      </template>

      <Background :gap="[14, 14]" :size="2" pattern-color="#eaeaea" class="vue-flow__background" />
      <MiniMap
        v-if="props.showMap"
        position="bottom-left"
        pannable
        zoomable
        class="vue-flow__minimap"
        :style="{
          width: 102,
          height: 72,
          bottom: '40px'
        }"
      />
      <Controls
        v-if="props.showControls"
        :can-undo="canUndo"
        :can-redo="canRedo"
        :history-past-states="historyPastStates"
        :history-future-states="historyFutureStates"
        :current-history-index="currentHistoryIndex"
        @undo="undo"
        @redo="redo"
        @history="controlshistory"
        @export-to-image="exportToImage"
        @layout-graph="layoutGraph"
        @add-note="addNote"
        @zoom-in="handleZoomIn"
        @zoom-out="handleZoomOut"
        @fit-view="handleFitView"
        @node-add="handleNodeAdd"
        @jump-to-history-state="handleJumpToHistoryState"
      />
      <Panel ref="PaneDrawer" :node="nodeInfo" popup-container="#parentNode" @change="panelChange" />
      <RunPanel v-if="showRunPanelDrawer" ref="runPanelRef" @hideRunPanel="hideRunPanel" />
      <RunhistoryPanel
        v-if="showHistoryPanel"
        ref="HistoryPanelRef"
        :historyData="historyData"
        @hideRunPanel="hideHistoryPanel"
      />
      <PublishTollModal ref="PublishTollModalRef" :flowData="flowData" :workflowInfo="workflowInfo" />
      <versionHistory
        v-if="showversionHistoryPanel"
        ref="versionHistoryref"
        :newHash="newHash"
        :versionData="versionData"
        @changeworkflow="changeworkflowpanel"
        @hideversionPanel="hideVersionhistoryPanel"
      />
    </VueFlow>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, provide, watch } from 'vue'
import dagre from 'dagre'
import type { Edge, Node, ConnectionMode } from '@vue-flow/core'
import type { EdgeChange, EdgeRemoveChange, NodeChange, VueFlowStore } from '@vue-flow/core'

import { useVueFlow, VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'
import { toJpeg, toPng, toSvg } from 'html-to-image'
import Controls from './controls/index.vue'
import CustomNode from './nodes/CustomNode.vue'
import StartNode from './nodes/start/index.vue'
import EndNode from './nodes/end/index.vue'
import AssignerNode from './nodes/assigner/index.vue'
import DocumentextractorNode from './nodes/document-extractor/index.vue'
import IfElseNode from './nodes/if-else/index.vue'
import IterationNode from './nodes/iteration/index.vue'
import ParameterExtractorNode from './nodes/parameter-extractor/index.vue'
import QuestionClassifierNode from './nodes/question-classifier/index.vue'
import variableAssignerExtractorNode from './nodes/variable-assigner/index.vue'
import HttpRequestNode from './nodes/http/index.vue'
import LlmNode from './nodes/llm/index.vue'
import AnswerNode from './nodes/answer/index.vue'
import VariableAggregatorNode from './nodes/variable-aggregator/index.vue'
import KnowledgRetrievalNode from './nodes/knowledge-retrieval/index.vue'
import CodeNode from './nodes/code/index.vue'
import IterationiStartNode from './nodes/IterationiStartNode.vue'
import ToolNode from './nodes/tool/index.vue'
import versionHistory from './version/index.vue'
import CustomEdge from './edges/index.vue'

import '@vue-flow/minimap/dist/style.css'
import Panel from './nodes/panel.vue'
import RunPanel from './run/index.vue'
import RunhistoryPanel from './run/history.vue'
import { nanoid } from 'nanoid'
import { getWorkflow, saveWorkflow, draftConfig, defaultConfig, getWorkflowInfo } from '@/apis'
import { debounce } from 'lodash-es'
import PublishTollModal from './modal/PublishTollModal.vue'
import { Message } from '@arco-design/web-vue'
import { NodeType } from './types/node'
import useNodeHook from './hooks/useNodeHook'
import useEdgeHook from './hooks/useEdgeHook'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import nodeUtils from './utils/node-utils'
import { log } from 'node:console'
defineOptions({ name: 'Workflow' })

const PublishTollModalRef = ref<InstanceType<typeof PublishTollModal>>()
const props = withDefaults(
  defineProps<{
    showMap?: boolean
    showControls?: boolean
    flowId?: string
  }>(),
  {
    showMap: true,
    showControls: true,
    flowId: ''
  }
)
const instance = ref()
const route = useRoute()
const emits = defineEmits(['workflowChange'])

const nodesStore = useNodesStore()

const appId = route.params.appId as string
const { addEdgesFn, onEdgeHoverStart, onEdgeHoverEnd } = useEdgeHook(instance, appId)
const { onNodeClick, showConfirmRemoveModal, handleNodeDrag, checkCyclicConnection } = useNodeHook(instance, appId)
interface VueFlowChanges {
  nodeChange: NodeChange[]
  edgeChange: EdgeChange[]
}

const onViewportChange = (viewport) => {
  // console.log('Viewport:', viewport)
}
const {
  onNodesChange,
  onEdgesChange,
  onConnect,
  nodes,
  edges,
  fitView,
  findNode,
  getNodes,
  addEdges,
  getEdges,
  getSelectedEdges,
  getSelectedNodes,
  updateNodeData,
  onPaneReady,
  onPaneClick,
  applyNodeChanges,
  applyEdgeChanges,
  setNodes,
  setEdges,
  addNodes
  // onNodesChange,
  // onEdgesChange
} = useVueFlow()

function configChange(changes: Array<EdgeChange | NodeChange>, type: 'node' | 'edge') {
  if (changes.length === 0) return false
  new Promise((resolve: (value: VueFlowChanges) => any) => {
    const license_change: VueFlowChanges = {
      nodeChange: [],
      edgeChange: []
    }
    changes.forEach(async (change, index) => {
      if (change.type === 'remove') {
        switch (type) {
          case 'node': {
            change as NodeChange
            const removeNode = findNode(change.id)
            if (removeNode) {
              switch (removeNode.data.type) {
                case NodeType.开始: {
                  Message.warning('开始节点无法删除')
                  break
                }
                case NodeType.迭代: {
                  const title = removeNode.data.type === NodeType.循环 ? '循环' : '迭代'
                  const childNodes = getNodes.value.filter((n) => n.parentNode === removeNode.id)
                  if (childNodes.length > 1) {
                    const confirmRemove = await showConfirmRemoveModal(title)
                    if (confirmRemove) {
                      license_change.nodeChange.push(change)
                      childNodes.forEach((n) => {
                        if (n.parentNode === removeNode.id) {
                          license_change.nodeChange.push({
                            id: n.id,
                            type: 'remove'
                          })
                        }
                      })
                    }
                  } else {
                    license_change.nodeChange.push(change)
                    childNodes.forEach((n) => {
                      if (n.parentNode === removeNode.id) {
                        license_change.nodeChange.push({
                          id: n.id,
                          type: 'remove'
                        })
                      }
                    })
                  }
                  break
                }
                default: {
                  license_change.nodeChange.push(change)
                  break
                }
              }

              const remove_node_ids = license_change.nodeChange
                .filter((change) => change.type === 'remove')
                .map((i) => i.id)
              const removeEdges = getEdges.value.filter(
                (e) => remove_node_ids.includes(e.source) || remove_node_ids.includes(e.target)
              )
              removeEdges.forEach((e) => {
                const { id, source, target } = e
                license_change.edgeChange.push({
                  id,
                  source,
                  sourceHandle: e.sourceHandle!,
                  target,
                  targetHandle: e.targetHandle!,
                  type: 'remove'
                } as EdgeChange)
              })
            }
            break
          }
          case 'edge': {
            if (getSelectedEdges.value.length === 0) {
              // 在删除节点时，默认会删除上下游的线，此处需要等待节点删除完毕再判断被删除线的上下游节点是否存在
              await nextTick()
              const sourceNode = findNode((change as EdgeRemoveChange).source)
              const targetNode = findNode((change as EdgeRemoveChange).target)
              if (!sourceNode || !targetNode) {
                license_change.edgeChange.push(change as EdgeChange)
              }
            } else {
              license_change.edgeChange.push(change as EdgeChange)
            }
            break
          }
        }
      } else {
        switch (type) {
          case 'node': {
            license_change.nodeChange.push(change as NodeChange)
            break
          }
          case 'edge': {
            license_change.edgeChange.push(change as EdgeChange)
            break
          }
        }
      }
      if (index === changes.length - 1) resolve(license_change)
    })
  }).then((license_change) => {
    license_change.nodeChange.length > 0 && applyNodeChanges(license_change.nodeChange)
    license_change.edgeChange.length > 0 && applyEdgeChanges(license_change.edgeChange)
  })
}
onNodesChange((changes) => configChange(changes, 'node'))
onEdgesChange((changes) => configChange(changes, 'edge'))
const nodesData = ref<Node[]>([])

const edgesData = ref<Edge[]>([])
const popoverInstance = ref(true)
provide('popoverInstance', popoverInstance)

const PaneDrawer = ref<InstanceType<typeof Panel>>()

const nodeInfo = ref()
const runPanelRef = ref()
const HistoryPanelRef = ref()
const versionHistoryref = ref()
const showRunPanelDrawer = ref(false)
const showHistoryPanel = ref(false)
const showversionHistoryPanel = ref(false)

const historyData = ref({})
const onNodesClick = ({ event, node }) => {
  console.log(node)

  PaneDrawer.value?.handleClick(event, node)
  nodeInfo.value = node
  onNodeClick(node)
}
const panelChange = (e) => {
  updateNodeData(nodeInfo.value.id, e)
}
const flowclick = () => {
  popoverInstance.value = false
  setTimeout(() => {
    popoverInstance.value = !popoverInstance.value
  }, 10)
}

// 节点拖拽事件处理已移动到 onNodesChange 监听器中

const undoStack = ref([])
const redoStack = ref([])
const maxHistorySize = 50 // 限制历史记录大小

// 计算撤销重做状态
const canUndo = computed(() => undoStack.value.length > 0)
const canRedo = computed(() => redoStack.value.length > 0)

// 工作流历史事件类型 - 参考 dify 的实现
enum WorkflowHistoryEvent {
  NodeTitleChange = 'NodeTitleChange',
  NodeDescriptionChange = 'NodeDescriptionChange',
  NodeDragStop = 'NodeDragStop',
  NodeChange = 'NodeChange',
  NodeConnect = 'NodeConnect',
  NodePaste = 'NodePaste',
  NodeDelete = 'NodeDelete',
  EdgeDelete = 'EdgeDelete',
  EdgeDeleteByDeleteBranch = 'EdgeDeleteByDeleteBranch',
  NodeAdd = 'NodeAdd',
  NodeResize = 'NodeResize',
  NoteAdd = 'NoteAdd',
  NoteChange = 'NoteChange',
  NoteDelete = 'NoteDelete',
  LayoutOrganize = 'LayoutOrganize'
}

// 历史状态接口
interface HistoryState {
  event: WorkflowHistoryEvent
  timestamp: number
  stepCount: number
  nodes: any[]
  edges: any[]
}

// 历史状态管理 - 简化版本，初始为空
const historyPastStates = ref<HistoryState[]>([])
const historyFutureStates = ref<HistoryState[]>([])
const currentHistoryIndex = ref(0)

// 防抖保存历史状态的引用
const saveStateToHistoryDebounced = ref<any>(null)

// 保存操作前的状态作为历史状态
let stateBeforeOperation: HistoryState | null = null

// 在操作前保存当前状态
const saveStateBeforeOperation = (event: WorkflowHistoryEvent) => {
  if (!getNodes || !getEdges) {
    console.warn('VueFlow instance not ready yet.')
    return
  }

  stateBeforeOperation = {
    event,
    timestamp: Date.now(),
    stepCount: 0,
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value))
  }
}

// 实际保存历史状态的函数（保存操作前的状态）
const doSaveStateToHistory = (event: WorkflowHistoryEvent) => {
  if (!stateBeforeOperation) {
    console.warn('No state before operation saved.')
    return
  }

  // 将操作前的状态添加到过去状态
  historyPastStates.value.push(stateBeforeOperation)

  // 清空未来状态（因为有了新的操作）
  historyFutureStates.value = []

  // 更新当前历史索引
  currentHistoryIndex.value = historyPastStates.value.length - 1

  // 限制历史记录大小
  if (historyPastStates.value.length > maxHistorySize) {
    historyPastStates.value.shift()
    currentHistoryIndex.value = historyPastStates.value.length - 1
  }

  // 更新步数计算
  updateHistoryStepCounts()

  // 清空临时状态
  stateBeforeOperation = null
}

// 参考 dify 的防抖保存历史状态
const saveStateToHistory = (event: WorkflowHistoryEvent) => {
  console.log(event)

  // 初始化防抖函数
  if (!saveStateToHistoryDebounced.value) {
    saveStateToHistoryDebounced.value = debounce((event: WorkflowHistoryEvent) => {
      doSaveStateToHistory(event)
    }, 500)
  }

  // 根据事件类型决定是否需要防抖
  switch (event) {
    case WorkflowHistoryEvent.NoteChange:
      saveStateToHistoryDebounced.value(event)
      break
    case WorkflowHistoryEvent.NodeTitleChange:
    case WorkflowHistoryEvent.NodeDescriptionChange:
    case WorkflowHistoryEvent.NodeDragStop:
    case WorkflowHistoryEvent.NodeChange:
    case WorkflowHistoryEvent.NodeConnect:
    case WorkflowHistoryEvent.NodePaste:
    case WorkflowHistoryEvent.NodeDelete:
    case WorkflowHistoryEvent.EdgeDelete:
    case WorkflowHistoryEvent.EdgeDeleteByDeleteBranch:
    case WorkflowHistoryEvent.NodeAdd:
    case WorkflowHistoryEvent.NodeResize:
    case WorkflowHistoryEvent.NoteAdd:
    case WorkflowHistoryEvent.LayoutOrganize:
    case WorkflowHistoryEvent.NoteDelete:
      saveStateToHistoryDebounced.value(event)
      break
    default:
      // 不为其他事件创建历史状态
      break
  }
}

// 更新历史状态的步数计算
const updateHistoryStepCounts = () => {
  // 为过去状态设置负步数（撤销）
  historyPastStates.value.forEach((state, index) => {
    state.stepCount = -(historyPastStates.value.length - index)
  })

  // 为未来状态设置正步数（重做）
  historyFutureStates.value.forEach((state, index) => {
    state.stepCount = index + 1
  })
}

// 兼容原有的 saveState 函数
const saveState = () => {
  if (!getNodes || !getEdges) {
    console.warn('VueFlow instance not ready yet.')
    return
  }

  const currentState = {
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value)),
    timestamp: Date.now()
  }

  // 限制历史记录大小
  if (undoStack.value.length >= maxHistorySize) {
    undoStack.value.shift()
  }

  undoStack.value.push(currentState)
  redoStack.value = [] // 清空重做栈
}
// 节点拖拽状态管理
const dragNodeStartPosition = ref({ x: 0, y: 0 })
const dragNodeId = ref<string | null>(null)
const isDragging = ref(false)

// 监听节点和边的变化，保存历史状态
onNodesChange((changes) => {
  // 保存到原有的撤销重做栈
  saveState()

  // 根据变化类型保存到历史状态 - 参考 dify 的实现
  changes.forEach((change) => {
    switch (change.type) {
      case 'add':
        // 节点添加在 handleNodeAdd 中已经处理
        break
      case 'remove':
        saveStateToHistory(WorkflowHistoryEvent.NodeDelete)
        break
      case 'position':
        // 处理位置变化的开始和结束
        if (change.dragging && !isDragging.value) {
          // 拖拽开始 - 保存操作前状态
          const node = getNodes.value.find((n) => n.id === change.id)
          if (node) {
            // 保存拖拽前的状态
            saveStateBeforeOperation(WorkflowHistoryEvent.NodeDragStop)

            // 记录拖拽开始位置
            dragNodeStartPosition.value = { x: node.position.x, y: node.position.y }
            dragNodeId.value = change.id
            isDragging.value = true
            console.log('Drag start - node:', change.id, 'saved state before operation')
          }
        } else if (!change.dragging && isDragging.value && dragNodeId.value === change.id) {
          const node = getNodes.value.find((n) => n.id === change.id)
          const { x, y } = dragNodeStartPosition.value
          // 拖拽结束 - 检查是否需要保存历史
          const newPos = node.position

          console.log('Drag end - start:', { x, y }, 'end:', newPos)

          // 只有当位置真正发生变化时才保存历史状态
          if (!(x === newPos.x && y === newPos.y)) {
            // 检查是否是有效的拖拽
            if (x !== 0 && y !== 0) {
              console.log('Saving drag stop history')
              doSaveStateToHistory(WorkflowHistoryEvent.NodeDragStop)
            }
          } else {
            // 位置没有变化，清空临时保存的状态
            stateBeforeOperation = null
          }

          // 重置拖拽状态
          isDragging.value = false
          dragNodeId.value = null
          dragNodeStartPosition.value = { x: 0, y: 0 }
        }
        break
      default:
        // 其他变化暂时不记录历史
        break
    }
  })
})

onEdgesChange((changes) => {
  // 保存到原有的撤销重做栈
  saveState()

  // 根据变化类型保存到历史状态
  changes.forEach((change) => {
    switch (change.type) {
      case 'add':
        // 连接事件在 onConnect 中处理
        break
      case 'remove':
        saveStateToHistory(WorkflowHistoryEvent.EdgeDelete)
        break
      default:
        break
    }
  })
})

const conhistorylist = ref([])
//撤销
const undo = () => {
  // 原有的撤销逻辑
  if (undoStack.value.length === 0) return

  const prevState = undoStack.value.pop()
  if (!prevState) return

  // 保存当前状态到重做栈
  redoStack.value.push({
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value))
  })

  // 应用撤销状态
  setNodes(prevState.nodes)
  setEdges(prevState.edges)
  edgesData.value = prevState.edges
  const nodes = prevState.nodes
  nodesData.value = nodes.map((e: any) => {
    if (e?.parentId) {
      e.parentNode = e.parentId
      e['extent'] = 'parent'
      e['expandParent'] = true
    }
    e.type = e.data.type
    return e
  })

  // 更新历史状态管理
  updateHistoryOnUndo()
}

// 更新历史状态（撤销时）
const updateHistoryOnUndo = () => {
  if (historyPastStates.value.length === 0) return

  // 将最后一个过去状态移动到未来状态
  const lastPastState = historyPastStates.value.pop()
  if (lastPastState) {
    historyFutureStates.value.unshift(lastPastState)
  }

  // 更新当前索引
  currentHistoryIndex.value = Math.max(0, historyPastStates.value.length - 1)

  // 更新步数计算
  updateHistoryStepCounts()
}
//重做
const redo = () => {
  // 原有的重做逻辑
  if (redoStack.value.length === 0) return

  const nextState = redoStack.value.pop()
  if (!nextState) return

  // 保存当前状态到撤销栈
  undoStack.value.push({
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value))
  })

  // 应用重做状态
  setNodes(nextState.nodes)
  setEdges(nextState.edges)
  edgesData.value = nextState.edges
  const nodes = nextState.nodes
  nodesData.value = nodes.map((e: any) => {
    if (e?.parentId) {
      e.parentNode = e.parentId
      e['extent'] = 'parent'
      e['expandParent'] = true
    }
    e.type = e.data.type
    return e
  })

  // 更新历史状态管理
  updateHistoryOnRedo()
}

// 更新历史状态（重做时）
const updateHistoryOnRedo = () => {
  if (historyFutureStates.value.length === 0) return

  // 将第一个未来状态移动到过去状态
  const firstFutureState = historyFutureStates.value.shift()
  if (firstFutureState) {
    historyPastStates.value.push(firstFutureState)
  }

  // 更新当前索引
  currentHistoryIndex.value = historyPastStates.value.length - 1

  // 更新步数计算
  updateHistoryStepCounts()
}
//变更历史
const controlshistory = async () => {}
const exportToImage = async (type: string) => {
  try {
    console.log(workflowInfo)
    const flowElement = document.querySelector('.custom-vue-flow') as HTMLElement // 获取画布 DOM 元素
    if (!flowElement) {
      Message.error('未找到工作流画布')
      return
    }

    let dataUrl: string
    const options = {
      quality: 1,
      pixelRatio: 2,
      backgroundColor: '#ffffff'
    }

    switch (type) {
      case 'png':
        dataUrl = await toPng(flowElement, options)
        break
      case 'jpeg':
      case 'jpg':
        dataUrl = await toJpeg(flowElement, options)
        break
      case 'svg':
        dataUrl = await toSvg(flowElement)
        break
      default:
        dataUrl = await toPng(flowElement, options)
    }

    const link = document.createElement('a')
    link.href = dataUrl
    link.download = (workflowInfo.value?.name || 'workflow') + '.' + type
    link.click()

    Message.success(`工作流已导出为 ${type.toUpperCase()} 格式`)
  } catch (error) {
    console.error('导出图片失败:', error)
    Message.error('导出图片失败')
  }
}

// 添加注释节点
const addNote = () => {
  try {
    const { getViewport } = useVueFlow()
    const viewport = getViewport()
    const noteNode = {
      id: `note_${nanoid()}`,
      type: 'note',
      position: {
        x: viewport.x + 100,
        y: viewport.y + 100
      },
      data: {
        title: '注释',
        content: '在此添加注释内容...',
        theme: 'yellow',
        showAuthor: false,
        type: 'note'
      }
    }

    // 添加到节点数组
    nodesData.value.push(noteNode)
    nodesStore.setNodes(nodesData.value)

    // 保存状态到历史记录
    saveStateToHistory(WorkflowHistoryEvent.NoteAdd)

    Message.success('已添加注释节点')
  } catch (error) {
    console.error('添加注释失败:', error)
    Message.error('添加注释失败')
  }
}

// 缩放控制函数
const handleZoomIn = () => {
  const { zoomIn } = useVueFlow()
  zoomIn()
}

const handleZoomOut = () => {
  const { zoomOut } = useVueFlow()
  zoomOut()
}

const handleFitView = () => {
  fitView({ padding: 0.1 })
}

// 处理节点添加
const handleNodeAdd = async (type: any, toolDefaultValue?: any) => {
  try {
    // 保存操作前状态
    saveStateBeforeOperation(WorkflowHistoryEvent.NodeAdd)

    // 创建新节点的数据
    const nodeData = {
      type,
      title: getNodeTitle(type),
      ...toolDefaultValue
    }

    // 使用现有的节点添加逻辑
    const { newNodeProps } = await nodeUtils().addNodeToflow({}, nodeData, '0')

    // 如果是迭代类型，动态添加子节点
    if (type === 'iteration') {
      nextTick(() => {
        addNodes({
          id: `node_${nanoid()}`,
          type: 'iteration-start',
          position: { x: 40, y: 80 },
          data: { title: '', desc: '', type: 'iteration-start', isInIteration: true },
          parentNode: newNodeProps.id,
          extent: 'parent',
          expandParent: true
        })
      })
    }

    // 保存操作前状态到历史记录
    doSaveStateToHistory(WorkflowHistoryEvent.NodeAdd)

    // 保存状态到原有的撤销重做栈
    saveState()

    Message.success(`已添加${nodeData.title}节点`)
  } catch (error) {
    console.error('添加节点失败:', error)
    Message.error('添加节点失败')
  }
}

// 获取节点标题
const getNodeTitle = (type: string) => {
  const titles: Record<string, string> = {
    llm: 'LLM',
    'knowledge-retrieval': '知识检索',
    answer: '直接回复',
    end: '结束',
    'if-else': '条件分支',
    iteration: '迭代',
    code: '代码执行',
    'http-request': 'HTTP请求',
    'parameter-extractor': '参数提取',
    'variable-aggregator': '变量聚合',
    assigner: '变量赋值',
    'document-extractor': '文档提取',
    tool: '工具'
  }
  return titles[type] || type
}

// 处理历史状态跳转
const handleJumpToHistoryState = (state: HistoryState) => {
  try {
    console.log('跳转到历史状态:', state)

    // 直接应用历史状态中的节点和边数据
    setNodes(state.nodes)
    setEdges(state.edges)
    edgesData.value = state.edges
    nodesData.value = state.nodes.map((e: any) => {
      if (e?.parentId) {
        e.parentNode = e.parentId
        e['extent'] = 'parent'
        e['expandParent'] = true
      }
      e.type = e.data.type
      return e
    })

    // 重新组织历史状态数组
    // 检查目标状态在过去状态中的位置
    const pastIndex = historyPastStates.value.findIndex((s) => s.timestamp === state.timestamp)
    if (pastIndex !== -1) {
      // 目标状态在过去状态中
      if (pastIndex < historyPastStates.value.length - 1) {
        // 将目标状态之后的所有过去状态移动到未来状态的开头
        const statesToMove = historyPastStates.value.splice(pastIndex + 1)
        historyFutureStates.value = [...statesToMove, ...historyFutureStates.value]
      }
      currentHistoryIndex.value = pastIndex
    } else {
      // 检查目标状态是否在未来状态中
      const futureIndex = historyFutureStates.value.findIndex((s) => s.timestamp === state.timestamp)
      if (futureIndex !== -1) {
        // 目标状态在未来状态中
        // 将目标状态及其之前的所有未来状态移动到过去状态的末尾
        const statesToMove = historyFutureStates.value.splice(0, futureIndex + 1)
        historyPastStates.value = [...historyPastStates.value, ...statesToMove]
        currentHistoryIndex.value = historyPastStates.value.length - 1
      }
    }

    // 更新步数计算
    updateHistoryStepCounts()

    // 同步更新原有的撤销重做栈，避免状态不一致
    const currentState = {
      nodes: JSON.parse(JSON.stringify(state.nodes)),
      edges: JSON.parse(JSON.stringify(state.edges)),
      timestamp: Date.now()
    }
    undoStack.value.push(currentState)
    redoStack.value = [] // 清空重做栈

    Message.success('已跳转到指定历史状态')
  } catch (error) {
    console.error('跳转历史状态失败:', error)
    Message.error('跳转历史状态失败')
  }
}
const direction = ref('LR') // 或 'LR'
const layoutGraph = async () => {
  const g = new dagre.graphlib.Graph()
  g.setGraph({ rankdir: direction.value, nodesep: 50, ranksep: 70 })
  g.setDefaultEdgeLabel(() => ({}))
  getNodes.value.forEach((node) => {
    g.setNode(node.id, { width: node.width || 100, height: node.height || 50 })
  })
  getEdges.value.forEach((edge) => {
    g.setEdge(edge.source, edge.target)
  })
  dagre.layout(g)
  const updatedNodes = getNodes.value.map((node) => {
    const { x, y } = g.node(node.id)
    return { ...node, position: { x, y } }
  })
  setNodes(updatedNodes)

  // 保存状态到历史记录
  saveStateToHistory(WorkflowHistoryEvent.LayoutOrganize)

  nextTick(() => {
    fitView()
  })
}
const save = () => {
  console.log(nodes)
  console.log(edges)
}
const newHash = ref()

const flowData = ref()
const isEnd = ref(false)
const workflowInfo = ref()
const getDraftConfig = async () => {
  const res = await getWorkflowInfo(appId)
  workflowInfo.value = res
}

const changeworkflowpanel = (item) => {
  edgesData.value = item.graph.edges
  const nodes = item.graph.nodes as any
  nodesData.value = nodes.map((e) => {
    if (e?.parentId) {
      e.parentNode = e.parentId
      e['extent'] = 'parent'
      e['expandParent'] = true
    }
    e.type = e.data.type
    return e
  })
}

const getWorkflowData = async () => {
  try {
    const res = await getWorkflow(appId)
    flowData.value = res
    const nodes = res.graph.nodes as any
    nodesData.value = nodes.map((e) => {
      if (e?.parentId) {
        e.parentNode = e.parentId
        e['extent'] = 'parent'
        e['expandParent'] = true
      }
      e.type = e.data.type
      return e
    })
    edgesData.value = res.graph.edges
    // nodes和edges放在store中
    nodesStore.setNodes(nodesData.value)
    nodesStore.setEdges(edgesData.value)
    isEnd.value = true
    newHash.value = res.hash
    nextTick(() => {
      fitView()
    })
  } catch (error: any) {
    if (error && error.response && error.response.data) {
      if (error.response.data.code == 'draft_workflow_not_exist') {
        const params = {
          graph: {
            nodes: [
              {
                id: `node_${nanoid()}`,
                type: 'custom',
                position: { x: 80, y: 282 },
                data: { type: 'start', title: '开始', desc: '', variables: [] },
                sourcePosition: 'right',
                targetPosition: 'left'
              }
            ],
            edges: []
          },
          features: {
            retriever_resource: { enabled: true }
          },
          environment_variables: [],
          conversation_variables: []
        }
        saveWorkflow(appId, JSON.stringify(params)).then(() => {
          getWorkflowData()
        })
      }
    }
  }
}
const saveData = debounce(async (newflowData) => {
  try {
    const res = await saveWorkflow(appId, JSON.stringify(newflowData))
    newHash.value = res.hash
  } catch (error: any) {
    if (error && error.json && !error.bodyUsed) {
      error.json().then((err: any) => {
        if (err.code === 'draft_workflow_not_sync') getWorkflowData()
      })
    }
  }
}, 4000)
const saveDatas = async (newflowData) => {
  console.log('saveData', newflowData)

  try {
    const res = await saveWorkflow(appId, JSON.stringify(newflowData))
    newHash.value = res.hash
    getWorkflowData()
  } catch (error: any) {
    if (error && error.json && !error.bodyUsed) {
      error.json().then((err: any) => {
        if (err.code === 'draft_workflow_not_sync') getWorkflowData()
      })
    }
  }
}
provide('saveworkFlowversion', { saveDatas })

watch(
  () => ({ nodes: nodesData.value, edges: edgesData.value }),
  (newVal) => {
    if (showversionHistoryPanel.value) {
      return false
    }
    if (isEnd.value) {
      const cloneNodes = JSON.parse(JSON.stringify(newVal.nodes))
      const newNodes = cloneNodes.map((e) => {
        if (e.data.type == 'iteration-start') {
          e.type = 'custom-iteration-start'
        } else {
          e.type = 'custom'
        }
        return e
      })
      const newflowData = {
        conversation_variables: flowData.value.conversation_variables,
        environment_variables: flowData.value.environment_variables,
        features: flowData.value,
        graph: {
          nodes: newNodes,
          edges: newVal.edges
        },
        hash: newHash.value
      }
      saveData(newflowData)
    }
  },
  { deep: true }
)
const handleConnect = (connection: any) => {
  console.log('handleConnect connection:', connection)

  // 安全检查
  if (!connection) {
    console.warn('handleConnect: connection is undefined')
    return
  }

  addEdges([
    {
      ...connection,
      type: 'custom',
      markerEnd: '',
      style: { strokeWidth: 2 }
    }
  ])

  // 连接完成后保存历史状态
  console.log('Saving connect history')
  saveStateToHistory(WorkflowHistoryEvent.NodeConnect)
}
onMounted(() => {
  getWorkflowData()
  getDraftConfig()
  onConnect((connection) => handleConnect(connection))
})

// 显示运行panel
const showRunPanel = () => {
  showRunPanelDrawer.value = true
  nextTick(() => {
    runPanelRef.value.visible = true
  })
}
const versionData = ref({})
const versionHistoryPanel = (item) => {
  versionData.value = item
  showversionHistoryPanel.value = true
  nextTick(() => {
    versionHistoryref.value.visible = true
  })
}
const showRunHistoryPanel = (item) => {
  historyData.value = item
  showHistoryPanel.value = true
  nextTick(() => {
    HistoryPanelRef.value.visible = true
  })
}
// 显示发布为工具panel
const showPublishTollPanel = () => {
  nextTick(() => {
    PublishTollModalRef.value?.onAdd()
  })
}
const hideRunPanel = () => {
  showRunPanelDrawer.value = false
}
const hideHistoryPanel = () => {
  showHistoryPanel.value = false
}
const hideVersionhistoryPanel = () => {
  showHistoryPanel.value = false
}

defineExpose({
  showRunPanel,
  showRunHistoryPanel,
  showPublishTollPanel,
  getWorkflowData,
  versionHistoryPanel
})
</script>

<style scoped lang="scss">
.vue-flow__background {
  background: #f5f7fd;
}

.vue-flow__minimap {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
