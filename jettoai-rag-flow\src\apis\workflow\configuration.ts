import http from '@/utils/http'

const BASE_URL = 'console/api'

// /workspaces/current/models/model-types/${type}
export const getModelListHttp = (type: any) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/models/model-types/${type}`)
}
// /workspaces/current/default-model?model_type=${type}
export const getDefaultModelHttp = (type: any) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/default-model?model_type=${type}`)
}
