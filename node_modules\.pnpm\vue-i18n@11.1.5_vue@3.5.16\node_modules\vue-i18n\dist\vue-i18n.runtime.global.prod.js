/*!
  * vue-i18n v11.1.5
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const n="undefined"!=typeof window,a=(e,t=!1)=>t?Symbol.for(e):Symbol(e),r=(e,t,n)=>l({l:e,k:t,s:n}),l=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),s=e=>"[object Date]"===F(e),i=e=>"[object RegExp]"===F(e),c=e=>T(e)&&0===Object.keys(e).length,u=Object.assign,m=Object.create,f=(e=null)=>m(e);function _(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const p=Object.prototype.hasOwnProperty;function g(e,t){return p.call(e,t)}const d=Array.isArray,b=e=>"function"==typeof e,h=e=>"string"==typeof e,v=e=>"boolean"==typeof e,k=e=>null!==e&&"object"==typeof e,E=e=>k(e)&&b(e.then)&&b(e.catch),y=Object.prototype.toString,F=e=>y.call(e),T=e=>"[object Object]"===F(e);function R(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const L=e=>!k(e)||d(e);function I(e,t){if(L(e)||L(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((a=>{"__proto__"!==a&&(k(e[a])&&!k(t[a])&&(t[a]=Array.isArray(e[a])?[]:f()),L(t[a])||L(e[a])?t[a]=e[a]:n.push({src:e[a],des:t[a]}))}))}}function N(e){return k(e)&&0===P(e)&&(g(e,"b")||g(e,"body"))}const O=["b","body"];const w=["c","cases"];const W=["s","static"];const M=["i","items"];const C=["t","type"];function P(e){return U(e,C)}const S=["v","value"];function D(e,t){const n=U(e,S);if(null!=n)return n;throw j(t)}const A=["m","modifier"];const $=["k","key"];function U(e,t,n){for(let a=0;a<t.length;a++){const n=t[a];if(g(e,n)&&null!=e[n])return e[n]}return n}const x=[...O,...w,...W,...M,...$,...A,...S,...C];function j(e){return new Error(`unhandled node type: ${e}`)}function H(e){return t=>function(e,t){const n=(a=t,U(a,O));var a;if(null==n)throw j(0);if(1===P(n)){const t=function(e){return U(e,w,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,V(e,n)]),[]))}return V(e,n)}(t,e)}function V(e,t){const n=function(e){return U(e,W)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return U(e,M,[])}(t).reduce(((t,n)=>[...t,G(e,n)]),[]);return e.normalize(n)}}function G(e,t){const n=P(t);switch(n){case 3:case 9:case 7:case 8:return D(t,n);case 4:{const a=t;if(g(a,"k")&&a.k)return e.interpolate(e.named(a.k));if(g(a,"key")&&a.key)return e.interpolate(e.named(a.key));throw j(n)}case 5:{const a=t;if(g(a,"i")&&o(a.i))return e.interpolate(e.list(a.i));if(g(a,"index")&&o(a.index))return e.interpolate(e.list(a.index));throw j(n)}case 6:{const n=t,a=function(e){return U(e,A)}(n),r=function(e){const t=U(e,$);if(t)return t;throw j(6)}(n);return e.linked(G(e,r),a?G(e,a):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}let Y=f();const z={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23};function X(e,t){return null!=t.locale?B(t.locale):B(e.locale)}let J;function B(e){if(h(e))return e;if(b(e)){if(e.resolvedOnce&&null!=J)return J;if("Function"===e.constructor.name){const t=e();if(E(t))throw Error(z.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return J=t}throw Error(z.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(z.NOT_SUPPORT_LOCALE_TYPE)}function q(e,t,n){return[...new Set([n,...d(t)?t:k(t)?Object.keys(t):h(t)?[t]:[n]])]}function K(e,t,n){const a=h(n)?n:ce,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let l=r.__localeChainCache.get(a);if(!l){l=[];let e=[n];for(;d(e);)e=Z(l,e,t);const o=d(t)||!T(t)?t:t.default?t.default:null;e=h(o)?[o]:o,d(e)&&Z(l,e,!1),r.__localeChainCache.set(a,l)}return l}function Z(e,t,n){let a=!0;for(let r=0;r<t.length&&v(a);r++){const l=t[r];h(l)&&(a=Q(e,t[r],n))}return a}function Q(e,t,n){let a;const r=t.split("-");do{a=ee(e,r.join("-"),n),r.splice(-1,1)}while(r.length&&!0===a);return a}function ee(e,t,n){let a=!1;if(!e.includes(t)&&(a=!0,t)){a="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(d(n)||T(n))&&n[r]&&(a=n[r])}return a}const te=[];te[0]={w:[0],i:[3,0],"[":[4],o:[7]},te[1]={w:[1],".":[2],"[":[4],o:[7]},te[2]={w:[2],i:[3,0],0:[3,0]},te[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},te[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},te[5]={"'":[4,0],o:8,l:[5,0]},te[6]={'"':[4,0],o:8,l:[6,0]};const ne=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ae(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function re(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,ne.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const le=new Map;function oe(e,t){return k(e)?e[t]:null}const se="11.1.5",ie=-1,ce="en-US",ue="",me=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let fe,_e,pe;let ge=null;const de=e=>{ge=e},be=()=>ge;let he=0;function ve(e={}){const t=b(e.onWarn)?e.onWarn:R,n=h(e.version)?e.version:se,a=h(e.locale)||b(e.locale)?e.locale:ce,r=b(a)?ce:a,l=d(e.fallbackLocale)||T(e.fallbackLocale)||h(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:r,o=T(e.messages)?e.messages:ke(r),s=T(e.datetimeFormats)?e.datetimeFormats:ke(r),c=T(e.numberFormats)?e.numberFormats:ke(r),m=u(f(),e.modifiers,{upper:(e,t)=>"text"===t&&h(e)?e.toUpperCase():"vnode"===t&&k(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&h(e)?e.toLowerCase():"vnode"===t&&k(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&h(e)?me(e):"vnode"===t&&k(e)&&"__v_isVNode"in e?me(e.children):e}),_=e.pluralRules||f(),p=b(e.missing)?e.missing:null,g=!v(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,E=!v(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,y=!!e.fallbackFormat,F=!!e.unresolving,L=b(e.postTranslation)?e.postTranslation:null,I=T(e.processor)?e.processor:null,N=!v(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,w=b(e.messageCompiler)?e.messageCompiler:fe,W=b(e.messageResolver)?e.messageResolver:_e||oe,M=b(e.localeFallbacker)?e.localeFallbacker:pe||q,C=k(e.fallbackContext)?e.fallbackContext:void 0,P=e,S=k(P.__datetimeFormatters)?P.__datetimeFormatters:new Map,D=k(P.__numberFormatters)?P.__numberFormatters:new Map,A=k(P.__meta)?P.__meta:{};he++;const $={version:n,cid:he,locale:a,fallbackLocale:l,messages:o,modifiers:m,pluralRules:_,missing:p,missingWarn:g,fallbackWarn:E,fallbackFormat:y,unresolving:F,postTranslation:L,processor:I,warnHtmlMessage:N,escapeParameter:O,messageCompiler:w,messageResolver:W,localeFallbacker:M,fallbackContext:C,onWarn:t,__meta:A};return $.datetimeFormats=s,$.numberFormats=c,$.__datetimeFormatters=S,$.__numberFormatters=D,$}const ke=e=>({[e]:f()});function Ee(e,t,n,a,r){const{missing:l,onWarn:o}=e;if(null!==l){const a=l(e,n,t,r);return h(a)?a:t}return t}function ye(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Fe(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let l=n+1;l<t.length;l++)if(a=e,r=t[l],a!==r&&a.split("-")[0]===r.split("-")[0])return!0;var a,r;return!1}function Te(e,...t){const{datetimeFormats:n,unresolving:a,fallbackLocale:r,onWarn:l,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[i,m,f,_]=Le(...t);v(f.missingWarn)?f.missingWarn:e.missingWarn;v(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const p=!!f.part,g=X(e,f),d=o(e,r,g);if(!h(i)||""===i)return new Intl.DateTimeFormat(g,_).format(m);let b,k={},E=null;for(let c=0;c<d.length&&(b=d[c],k=n[b]||{},E=k[i],!T(E));c++)Ee(e,i,b,0,"datetime format");if(!T(E)||!h(b))return a?ie:i;let y=`${b}__${i}`;c(_)||(y=`${y}__${JSON.stringify(_)}`);let F=s.get(y);return F||(F=new Intl.DateTimeFormat(b,u({},E,_)),s.set(y,F)),p?F.formatToParts(m):F.format(m)}const Re=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Le(...e){const[t,n,a,r]=e,l=f();let i,c=f();if(h(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(z.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(n);try{i.toISOString()}catch{throw Error(z.INVALID_ISO_DATE_ARGUMENT)}}else if(s(t)){if(isNaN(t.getTime()))throw Error(z.INVALID_DATE_ARGUMENT);i=t}else{if(!o(t))throw Error(z.INVALID_ARGUMENT);i=t}return h(n)?l.key=n:T(n)&&Object.keys(n).forEach((e=>{Re.includes(e)?c[e]=n[e]:l[e]=n[e]})),h(a)?l.locale=a:T(a)&&(c=a),T(r)&&(c=r),[l.key||"",i,l,c]}function Ie(e,t,n){const a=e;for(const r in n){const e=`${t}__${r}`;a.__datetimeFormatters.has(e)&&a.__datetimeFormatters.delete(e)}}function Ne(e,...t){const{numberFormats:n,unresolving:a,fallbackLocale:r,onWarn:l,localeFallbacker:o}=e,{__numberFormatters:s}=e,[i,m,f,_]=we(...t);v(f.missingWarn)?f.missingWarn:e.missingWarn;v(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const p=!!f.part,g=X(e,f),d=o(e,r,g);if(!h(i)||""===i)return new Intl.NumberFormat(g,_).format(m);let b,k={},E=null;for(let c=0;c<d.length&&(b=d[c],k=n[b]||{},E=k[i],!T(E));c++)Ee(e,i,b,0,"number format");if(!T(E)||!h(b))return a?ie:i;let y=`${b}__${i}`;c(_)||(y=`${y}__${JSON.stringify(_)}`);let F=s.get(y);return F||(F=new Intl.NumberFormat(b,u({},E,_)),s.set(y,F)),p?F.formatToParts(m):F.format(m)}const Oe=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function we(...e){const[t,n,a,r]=e,l=f();let s=f();if(!o(t))throw Error(z.INVALID_ARGUMENT);const i=t;return h(n)?l.key=n:T(n)&&Object.keys(n).forEach((e=>{Oe.includes(e)?s[e]=n[e]:l[e]=n[e]})),h(a)?l.locale=a:T(a)&&(s=a),T(r)&&(s=r),[l.key||"",i,l,s]}function We(e,t,n){const a=e;for(const r in n){const e=`${t}__${r}`;a.__numberFormatters.has(e)&&a.__numberFormatters.delete(e)}}const Me=e=>e,Ce=e=>"",Pe="text",Se=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,n,a)=>0===a?e+n:e+t+n),"")}(e),De=e=>null==e?"":d(e)||T(e)&&e.toString===y?JSON.stringify(e,null,2):String(e);function Ae(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function $e(e={}){const t=e.locale,n=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),a=k(e.pluralRules)&&h(t)&&b(e.pluralRules[t])?e.pluralRules[t]:Ae,r=k(e.pluralRules)&&h(t)&&b(e.pluralRules[t])?Ae:void 0,l=e.list||[],s=e.named||f();o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function i(t,n){const a=b(e.messages)?e.messages(t,!!n):!!k(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):Ce)}const c=T(e.processor)&&b(e.processor.normalize)?e.processor.normalize:Se,m=T(e.processor)&&b(e.processor.interpolate)?e.processor.interpolate:De,_={list:e=>l[e],named:e=>s[e],plural:e=>e[a(n,e.length,r)],linked:(t,...n)=>{const[a,r]=n;let l="text",o="";1===n.length?k(a)?(o=a.modifier||o,l=a.type||l):h(a)&&(o=a||o):2===n.length&&(h(a)&&(o=a||o),h(r)&&(l=r||l));const s=i(t,!0)(_),c="vnode"===l&&d(s)&&o?s[0]:s;return o?(u=o,e.modifiers?e.modifiers[u]:Me)(c,l):c;var u},message:i,type:T(e.processor)&&h(e.processor.type)?e.processor.type:Pe,interpolate:m,normalize:c,values:u(f(),l,s)};return _}const Ue=()=>"",xe=e=>b(e);function je(e,...t){const{fallbackFormat:n,postTranslation:a,unresolving:r,messageCompiler:l,fallbackLocale:s,messages:i}=e,[c,u]=Ge(...t),m=v(u.missingWarn)?u.missingWarn:e.missingWarn,p=v(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,g=v(u.escapeParameter)?u.escapeParameter:e.escapeParameter,E=!!u.resolvedMessage,y=h(u.default)||v(u.default)?v(u.default)?l?c:()=>c:u.default:n?l?c:()=>c:null,F=n||null!=y&&(h(y)||b(y)),T=X(e,u);g&&function(e){d(e.list)?e.list=e.list.map((e=>h(e)?_(e):e)):k(e.named)&&Object.keys(e.named).forEach((t=>{h(e.named[t])&&(e.named[t]=_(e.named[t]))}))}(u);let[R,L,I]=E?[c,T,i[T]||f()]:He(e,c,T,s,p,m),O=R,w=c;if(E||h(O)||N(O)||xe(O)||F&&(O=y,w=O),!(E||(h(O)||N(O)||xe(O))&&h(L)))return r?ie:c;let W=!1;const M=xe(O)?O:Ve(e,c,L,O,w,(()=>{W=!0}));if(W)return O;const C=function(e,t,n,a){const{modifiers:r,pluralRules:l,messageResolver:s,fallbackLocale:i,fallbackWarn:c,missingWarn:u,fallbackContext:m}=e,f=(a,r)=>{let l=s(n,a);if(null==l&&(m||r)){const[,,n]=He(m||e,a,t,i,c,u);l=s(n,a)}if(h(l)||N(l)){let n=!1;const r=Ve(e,a,t,l,a,(()=>{n=!0}));return n?Ue:r}return xe(l)?l:Ue},_={locale:t,modifiers:r,pluralRules:l,messages:f};e.processor&&(_.processor=e.processor);a.list&&(_.list=a.list);a.named&&(_.named=a.named);o(a.plural)&&(_.pluralIndex=a.plural);return _}(e,L,I,u),P=function(e,t,n){const a=t(n);return a}(0,M,$e(C));return a?a(P,c):P}function He(e,t,n,a,r,l){const{messages:o,onWarn:s,messageResolver:i,localeFallbacker:c}=e,u=c(e,a,n);let m,_=f(),p=null;for(let g=0;g<u.length&&(m=u[g],_=o[m]||f(),null===(p=i(_,t))&&(p=_[t]),!(h(p)||N(p)||xe(p)));g++)if(!Fe(m,u)){const n=Ee(e,t,m,0,"translate");n!==t&&(p=n)}return[p,m,_]}function Ve(e,t,n,a,l,o){const{messageCompiler:s,warnHtmlMessage:i}=e;if(xe(a)){const e=a;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>a;return e.locale=n,e.key=t,e}const c=s(a,function(e,t,n,a,l,o){return{locale:t,key:n,warnHtmlMessage:l,onError:e=>{throw o&&o(e),e},onCacheKey:e=>r(t,n,e)}}(0,n,l,0,i,o));return c.locale=n,c.key=t,c.source=a,c}function Ge(...e){const[t,n,a]=e,r=f();if(!(h(t)||o(t)||xe(t)||N(t)))throw Error(z.INVALID_ARGUMENT);const l=o(t)?String(t):(xe(t),t);return o(n)?r.plural=n:h(n)?r.default=n:T(n)&&!c(n)?r.named=n:d(n)&&(r.list=n),o(a)?r.plural=a:h(a)?r.default=a:T(a)&&u(r,a),[l,r]}const Ye="11.1.5",ze={UNEXPECTED_RETURN_TYPE:24,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34},Xe=a("__translateVNode"),Je=a("__datetimeParts"),Be=a("__numberParts"),qe=a("__setPluralRules"),Ke=a("__injectWithOption"),Ze=a("__dispose");function Qe(e){if(!k(e))return e;if(N(e))return e;for(const t in e)if(g(e,t))if(t.includes(".")){const n=t.split("."),a=n.length-1;let r=e,l=!1;for(let e=0;e<a;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in r||(r[n[e]]=f()),!k(r[n[e]])){l=!0;break}r=r[n[e]]}if(l||(N(r)?x.includes(n[a])||delete e[t]:(r[n[a]]=e[t],delete e[t])),!N(r)){const e=r[n[a]];k(e)&&Qe(e)}}else k(e[t])&&Qe(e[t]);return e}function et(e,t){const{messages:n,__i18n:a,messageResolver:r,flatJson:l}=t,o=T(n)?n:d(a)?f():{[e]:f()};if(d(a)&&a.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(o[t]=o[t]||f(),I(n,o[t])):I(n,o)}else h(e)&&I(JSON.parse(e),o)})),null==r&&l)for(const s in o)g(o,s)&&Qe(o[s]);return o}function tt(e){return e.type}function nt(e,t,n){let a=k(t.messages)?t.messages:f();"__i18nGlobal"in n&&(a=et(e.locale.value,{messages:a,__i18n:n.__i18nGlobal}));const r=Object.keys(a);if(r.length&&r.forEach((t=>{e.mergeLocaleMessage(t,a[t])})),k(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(k(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function at(e){return t.createVNode(t.Text,null,e,0)}const rt=()=>[],lt=()=>!1;let ot=0;function st(e){return(n,a,r,l)=>e(a,r,t.getCurrentInstance()||void 0,l)}function it(e={}){const{__root:a,__injectWithOption:r}=e,l=void 0===a,s=e.flatJson,c=n?t.ref:t.shallowRef;let m=!v(e.inheritLocale)||e.inheritLocale;const f=c(a&&m?a.locale.value:h(e.locale)?e.locale:ce),_=c(a&&m?a.fallbackLocale.value:h(e.fallbackLocale)||d(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:f.value),p=c(et(f.value,e)),E=c(T(e.datetimeFormats)?e.datetimeFormats:{[f.value]:{}}),y=c(T(e.numberFormats)?e.numberFormats:{[f.value]:{}});let F=a?a.missingWarn:!v(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,R=a?a.fallbackWarn:!v(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,L=a?a.fallbackRoot:!v(e.fallbackRoot)||e.fallbackRoot,O=!!e.fallbackFormat,w=b(e.missing)?e.missing:null,W=b(e.missing)?st(e.missing):null,M=b(e.postTranslation)?e.postTranslation:null,C=a?a.warnHtmlMessage:!v(e.warnHtmlMessage)||e.warnHtmlMessage,P=!!e.escapeParameter;const S=a?a.modifiers:T(e.modifiers)?e.modifiers:{};let D,A=e.pluralRules||a&&a.pluralRules;D=(()=>{l&&de(null);const t={version:Ye,locale:f.value,fallbackLocale:_.value,messages:p.value,modifiers:S,pluralRules:A,missing:null===W?void 0:W,missingWarn:F,fallbackWarn:R,fallbackFormat:O,unresolving:!0,postTranslation:null===M?void 0:M,warnHtmlMessage:C,escapeParameter:P,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=E.value,t.numberFormats=y.value,t.__datetimeFormatters=T(D)?D.__datetimeFormatters:void 0,t.__numberFormatters=T(D)?D.__numberFormatters:void 0;const n=ve(t);return l&&de(n),n})(),ye(D,f.value,_.value);const $=t.computed({get:()=>f.value,set:e=>{D.locale=e,f.value=e}}),U=t.computed({get:()=>_.value,set:e=>{D.fallbackLocale=e,_.value=e,ye(D,f.value,e)}}),x=t.computed((()=>p.value)),j=t.computed((()=>E.value)),H=t.computed((()=>y.value));const V=(e,t,n,r,s,i)=>{let c;f.value,_.value,p.value,E.value,y.value;try{0,l||(D.fallbackContext=a?be():void 0),c=e(D)}finally{l||(D.fallbackContext=void 0)}if("translate exists"!==n&&o(c)&&c===ie||"translate exists"===n&&!c){const[e,n]=t();return a&&L?r(a):s(e)}if(i(c))return c;throw Error(ze.UNEXPECTED_RETURN_TYPE)};function G(...e){return V((t=>Reflect.apply(je,null,[t,...e])),(()=>Ge(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>h(e)))}const Y={normalize:function(e){return e.map((e=>h(e)||o(e)||v(e)?at(String(e)):e))},interpolate:e=>e,type:"vnode"};function z(e){return p.value[e]||{}}ot++,a&&n&&(t.watch(a.locale,(e=>{m&&(f.value=e,D.locale=e,ye(D,f.value,_.value))})),t.watch(a.fallbackLocale,(e=>{m&&(_.value=e,D.fallbackLocale=e,ye(D,f.value,_.value))})));const X={id:ot,locale:$,fallbackLocale:U,get inheritLocale(){return m},set inheritLocale(e){m=e,e&&a&&(f.value=a.locale.value,_.value=a.fallbackLocale.value,ye(D,f.value,_.value))},get availableLocales(){return Object.keys(p.value).sort()},messages:x,get modifiers(){return S},get pluralRules(){return A||{}},get isGlobal(){return l},get missingWarn(){return F},set missingWarn(e){F=e,D.missingWarn=F},get fallbackWarn(){return R},set fallbackWarn(e){R=e,D.fallbackWarn=R},get fallbackRoot(){return L},set fallbackRoot(e){L=e},get fallbackFormat(){return O},set fallbackFormat(e){O=e,D.fallbackFormat=O},get warnHtmlMessage(){return C},set warnHtmlMessage(e){C=e,D.warnHtmlMessage=e},get escapeParameter(){return P},set escapeParameter(e){P=e,D.escapeParameter=e},t:G,getLocaleMessage:z,setLocaleMessage:function(e,t){if(s){const n={[e]:t};for(const e in n)g(n,e)&&Qe(n[e]);t=n[e]}p.value[e]=t,D.messages=p.value},mergeLocaleMessage:function(e,t){p.value[e]=p.value[e]||{};const n={[e]:t};if(s)for(const a in n)g(n,a)&&Qe(n[a]);I(t=n[e],p.value[e]),D.messages=p.value},getPostTranslationHandler:function(){return b(M)?M:null},setPostTranslationHandler:function(e){M=e,D.postTranslation=e},getMissingHandler:function(){return w},setMissingHandler:function(e){null!==e&&(W=st(e)),w=e,D.missing=W},[qe]:function(e){A=e,D.pluralRules=A}};return X.datetimeFormats=j,X.numberFormats=H,X.rt=function(...e){const[t,n,a]=e;if(a&&!k(a))throw Error(ze.INVALID_ARGUMENT);return G(t,n,u({resolvedMessage:!0},a||{}))},X.te=function(e,t){return V((()=>{if(!e)return!1;const n=z(h(t)?t:f.value),a=D.messageResolver(n,e);return N(a)||xe(a)||h(a)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),lt,(e=>v(e)))},X.tm=function(e){const t=function(e){let t=null;const n=K(D,_.value,f.value);for(let a=0;a<n.length;a++){const r=p.value[n[a]]||{},l=D.messageResolver(r,e);if(null!=l){t=l;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},X.d=function(...e){return V((t=>Reflect.apply(Te,null,[t,...e])),(()=>Le(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>ue),(e=>h(e)||d(e)))},X.n=function(...e){return V((t=>Reflect.apply(Ne,null,[t,...e])),(()=>we(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>ue),(e=>h(e)||d(e)))},X.getDateTimeFormat=function(e){return E.value[e]||{}},X.setDateTimeFormat=function(e,t){E.value[e]=t,D.datetimeFormats=E.value,Ie(D,e,t)},X.mergeDateTimeFormat=function(e,t){E.value[e]=u(E.value[e]||{},t),D.datetimeFormats=E.value,Ie(D,e,t)},X.getNumberFormat=function(e){return y.value[e]||{}},X.setNumberFormat=function(e,t){y.value[e]=t,D.numberFormats=y.value,We(D,e,t)},X.mergeNumberFormat=function(e,t){y.value[e]=u(y.value[e]||{},t),D.numberFormats=y.value,We(D,e,t)},X[Ke]=r,X[Xe]=function(...e){return V((t=>{let n;const a=t;try{a.processor=Y,n=Reflect.apply(je,null,[a,...e])}finally{a.processor=null}return n}),(()=>Ge(...e)),"translate",(t=>t[Xe](...e)),(e=>[at(e)]),(e=>d(e)))},X[Je]=function(...e){return V((t=>Reflect.apply(Te,null,[t,...e])),(()=>Le(...e)),"datetime format",(t=>t[Je](...e)),rt,(e=>h(e)||d(e)))},X[Be]=function(...e){return V((t=>Reflect.apply(Ne,null,[t,...e])),(()=>we(...e)),"number format",(t=>t[Be](...e)),rt,(e=>h(e)||d(e)))},X}function ct(e={}){const t=it(function(e){const t=h(e.locale)?e.locale:ce,n=h(e.fallbackLocale)||d(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,a=b(e.missing)?e.missing:void 0,r=!v(e.silentTranslationWarn)&&!i(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!v(e.silentFallbackWarn)&&!i(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!v(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,c=T(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,f=b(e.postTranslation)?e.postTranslation:void 0,_=!h(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,g=!v(e.sync)||e.sync;let k=e.messages;if(T(e.sharedMessages)){const t=e.sharedMessages;k=Object.keys(t).reduce(((e,n)=>{const a=e[n]||(e[n]={});return u(a,t[n]),e}),k||{})}const{__i18n:E,__root:y,__injectWithOption:F}=e,R=e.datetimeFormats,L=e.numberFormats;return{locale:t,fallbackLocale:n,messages:k,flatJson:e.flatJson,datetimeFormats:R,numberFormats:L,missing:a,missingWarn:r,fallbackWarn:l,fallbackRoot:o,fallbackFormat:s,modifiers:c,pluralRules:m,postTranslation:f,warnHtmlMessage:_,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:g,__i18n:E,__root:y,__injectWithOption:F}}(e)),{__extender:n}=e,a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return v(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=v(e)?!e:e},get silentFallbackWarn(){return v(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=v(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return a.__extender=n,a}function ut(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[qe](t.pluralizationRules||e.pluralizationRules);const n=et(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const mt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function ft(){return t.Fragment}const _t=t.defineComponent({name:"i18n-t",props:u({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>o(e)||!isNaN(e)}},mt),setup(e,n){const{slots:a,attrs:r}=n,l=e.i18n||yt({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(a).filter((e=>"_"!==e[0])),s=f();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=h(e.plural)?+e.plural:e.plural);const i=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const a=e[n];return a&&(t[n]=a()),t}),f())}(n,o),c=l[Xe](e.keypath,i,s),m=u(f(),r),_=h(e.tag)||k(e.tag)?e.tag:ft();return t.h(_,m,c)}}}),pt=_t;function gt(e,n,a,r){const{slots:l,attrs:o}=n;return()=>{const n={part:!0};let s=f();e.locale&&(n.locale=e.locale),h(e.format)?n.key=e.format:k(e.format)&&(h(e.format.key)&&(n.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>a.includes(n)?u(f(),t,{[n]:e.format[n]}):t),f()));const i=r(e.value,n,s);let c=[n.key];d(i)?c=i.map(((e,t)=>{const n=l[e.type],a=n?n({[e.type]:e.value,index:t,parts:i}):[e.value];var r;return d(r=a)&&!h(r[0])&&(a[0].key=`${e.type}-${t}`),a})):h(i)&&(c=[i]);const m=u(f(),o),_=h(e.tag)||k(e.tag)?e.tag:ft();return t.h(_,m,c)}}const dt=t.defineComponent({name:"i18n-n",props:u({value:{type:Number,required:!0},format:{type:[String,Object]}},mt),setup(e,t){const n=e.i18n||yt({useScope:e.scope,__useComponent:!0});return gt(e,t,Oe,((...e)=>n[Be](...e)))}}),bt=dt;function ht(e){const a=t=>{const{instance:n,value:a}=t;if(!n||!n.$)throw Error(ze.UNEXPECTED_ERROR);const r=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const a=n.__getInstance(t);return null!=a?a.__composer:e.global.__composer}}(e,n.$),l=vt(a);return[Reflect.apply(r.t,r,[...kt(l)]),r]};return{created:(r,l)=>{const[o,s]=a(l);n&&e.global===s&&(r.__i18nWatcher=t.watch(s.locale,(()=>{l.instance&&l.instance.$forceUpdate()}))),r.__composer=s,r.textContent=o},unmounted:e=>{n&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,a=vt(t);e.textContent=Reflect.apply(n.t,n,[...kt(a)])}},getSSRProps:e=>{const[t]=a(e);return{textContent:t}}}}function vt(e){if(h(e))return{path:e};if(T(e)){if(!("path"in e))throw Error(ze.REQUIRED_VALUE,"path");return e}throw Error(ze.INVALID_VALUE)}function kt(e){const{path:t,locale:n,args:a,choice:r,plural:l}=e,s={},i=a||{};return h(n)&&(s.locale=n),o(r)&&(s.plural=r),o(l)&&(s.plural=l),[t,i,s]}const Et=a("global-vue-i18n");function yt(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(ze.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(ze.NOT_INSTALLED);const a=function(e){const n=t.inject(e.isCE?Et:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw Error(e.isCE?ze.NOT_INSTALLED_WITH_PROVIDE:ze.UNEXPECTED_ERROR);return n}(n),r=function(e){return"composition"===e.mode?e.global:e.global.__composer}(a),l=tt(n),o=function(e,t){return c(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,l);if("global"===o)return nt(r,e,l),r;if("parent"===o){let t=function(e,t,n=!1){let a=null;const r=t.root;let l=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=l;){const t=e;if("composition"===e.mode)a=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(a=e.__composer,n&&a&&!a[Ke]&&(a=null))}if(null!=a)break;if(r===l)break;l=l.parent}return a}(a,n,e.__useComponent);return null==t&&(t=r),t}const s=a;let i=s.__getInstance(n);if(null==i){const a=u({},e);"__i18n"in l&&(a.__i18n=l.__i18n),r&&(a.__root=r),i=it(a),s.__composerExtend&&(i[Ze]=s.__composerExtend(i)),function(e,n,a){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=a;e.__deleteInstance(n);const r=t[Ze];r&&(r(),delete t[Ze])}),n)}(s,n,i),s.__setInstance(n,i)}return i}const Ft=["locale","fallbackLocale","availableLocales"],Tt=["t","rt","d","n","tm","te"];const Rt=t.defineComponent({name:"i18n-d",props:u({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},mt),setup(e,t){const n=e.i18n||yt({useScope:e.scope,__useComponent:!0});return gt(e,t,Re,((...e)=>n[Je](...e)))}}),Lt=Rt;return fe=function(e,t){{const t=e.cacheKey;if(t){const n=Y[t];return n||(Y[t]=H(e))}return H(e)}},_e=function(e,t){if(!k(e))return null;let n=le.get(t);if(n||(n=function(e){const t=[];let n,a,r,l,o,s,i,c=-1,u=0,m=0;const f=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===a?a=r:a+=r},f[1]=()=>{void 0!==a&&(t.push(a),a=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===a)return!1;if(a=re(a),!1===a)return!1;f[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!_()){if(l=ae(n),i=te[u],o=i[l]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(r=n,!1===s())))return;if(7===u)return t}}(t),n&&le.set(t,n)),!n)return null;const a=n.length;let r=e,l=0;for(;l<a;){const e=n[l];if(x.includes(e)&&N(r))return null;const t=r[e];if(void 0===t)return null;if(b(r))return null;r=t,l++}return r},pe=K,e.DatetimeFormat=Rt,e.I18nD=Lt,e.I18nInjectionKey=Et,e.I18nN=bt,e.I18nT=pt,e.NumberFormat=dt,e.Translation=_t,e.VERSION=Ye,e.createI18n=function(e={}){const n=!v(e.legacy)||e.legacy,r=!v(e.globalInjection)||e.globalInjection,l=new Map,[o,s]=function(e,n){const a=t.effectScope(),r=n?a.run((()=>ct(e))):a.run((()=>it(e)));if(null==r)throw Error(ze.UNEXPECTED_ERROR);return[a,r]}(e,n),i=a(""),c={get mode(){return n?"legacy":"composition"},async install(e,...a){if(e.__VUE_I18N_SYMBOL__=i,e.provide(e.__VUE_I18N_SYMBOL__,c),T(a[0])){const e=a[0];c.__composerExtend=e.__composerExtend,c.__vueI18nExtend=e.__vueI18nExtend}let l=null;!n&&r&&(l=function(e,n){const a=Object.create(null);Ft.forEach((e=>{const r=Object.getOwnPropertyDescriptor(n,e);if(!r)throw Error(ze.UNEXPECTED_ERROR);const l=t.isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(a,e,l)})),e.config.globalProperties.$i18n=a,Tt.forEach((t=>{const a=Object.getOwnPropertyDescriptor(n,t);if(!a||!a.value)throw Error(ze.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,a)}));const r=()=>{delete e.config.globalProperties.$i18n,Tt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return r}(e,c.global)),function(e,t,...n){const a=T(n[0])?n[0]:{};(!v(a.globalInstall)||a.globalInstall)&&([_t.name,"I18nT"].forEach((t=>e.component(t,_t))),[dt.name,"I18nN"].forEach((t=>e.component(t,dt))),[Rt.name,"I18nD"].forEach((t=>e.component(t,Rt)))),e.directive("t",ht(t))}(e,c,...a),n&&e.mixin(function(e,n,a){return{beforeCreate(){const r=t.getCurrentInstance();if(!r)throw Error(ze.UNEXPECTED_ERROR);const l=this.$options;if(l.i18n){const t=l.i18n;if(l.__i18n&&(t.__i18n=l.__i18n),t.__root=n,this===this.$root)this.$i18n=ut(e,t);else{t.__injectWithOption=!0,t.__extender=a.__vueI18nExtend,this.$i18n=ct(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(l.__i18n)if(this===this.$root)this.$i18n=ut(e,l);else{this.$i18n=ct({__i18n:l.__i18n,__injectWithOption:!0,__extender:a.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;l.__i18nGlobal&&nt(n,l,l),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),a.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(ze.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),a.__deleteInstance(e),delete this.$i18n}}}(s,s.__composer,c));const o=e.unmount;e.unmount=()=>{l&&l(),c.dispose(),o()}},get global(){return s},dispose(){o.stop()},__instances:l,__getInstance:function(e){return l.get(e)||null},__setInstance:function(e,t){l.set(e,t)},__deleteInstance:function(e){l.delete(e)}};return c},e.useI18n=yt,e.vTDirective=ht,e}({},Vue);
