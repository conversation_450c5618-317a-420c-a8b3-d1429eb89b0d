<template>
  <a-dropdown
    :visible="open"
    :trigger="['click']"
    :position="placement"
    :popup-max-height="400"
    :popup-container="'body'"
    @select="handleSelect"
    @visible-change="handleOpenChange"
  >
    <div :class="triggerClassName" :style="triggerStyle" @click="handleTrigger">
      <slot name="trigger" :open="open">
        <a-button type="text" class="block-selector-trigger">
          <template #icon>
            <icon-plus />
          </template>
          添加节点
        </a-button>
      </slot>
    </div>

    <template #content>
      <div class="block-selector-panel" @click.stop>
        <!-- 搜索框 -->
        <div class="search-section">
          <a-input v-model="searchText" :placeholder="searchPlaceholder" allow-clear auto-focus>
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </div>

        <!-- 标签页 -->
        <a-tabs v-model:active-key="activeTab" :lazy-load="false" class="block-tabs" @tab-click="handleTabChange">
          <a-tab-pane key="blocks" title="节点">
            <BlockList :search-text="searchText" :available-blocks="availableBlocksTypes" @select="handleBlockSelect" />
          </a-tab-pane>

          <a-tab-pane key="tools" title="工具">
            <ToolList :search-text="searchText" @select="handleToolSelect" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { BlockEnum } from '../../types/workflow'
import BlockList from './block-list.vue'
import ToolList from './tool-list.vue'

interface Props {
  open?: boolean
  disabled?: boolean
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end'
  triggerClassName?: string
  triggerStyle?: Record<string, any>
  availableBlocksTypes?: BlockEnum[]
}

interface Emits {
  (e: 'openChange', open: boolean): void
  (e: 'select', type: BlockEnum, toolDefaultValue?: any): void
}

const props = withDefaults(defineProps<Props>(), {
  open: undefined,
  disabled: false,
  placement: 'bottom-start',
  availableBlocksTypes: () => Object.values(BlockEnum)
})

const emit = defineEmits<Emits>()

// 响应式数据
const searchText = ref('')
const activeTab = ref('blocks')
const localOpen = ref(false)

// 计算属性
const open = computed(() => (props.open !== undefined ? props.open : localOpen.value))

const searchPlaceholder = computed(() => {
  return activeTab.value === 'blocks' ? '搜索节点...' : '搜索工具...'
})

// 方法
const handleOpenChange = (visible: boolean) => {
  if (props.disabled) return

  localOpen.value = visible
  emit('openChange', visible)

  if (!visible) {
    searchText.value = ''
  }
}

const handleTrigger = (e: Event) => {
  if (props.disabled) return
  e.stopPropagation()
  handleOpenChange(!open.value)
}

const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleBlockSelect = (type: BlockEnum) => {
  handleOpenChange(false)
  emit('select', type)
}

const handleToolSelect = (type: BlockEnum, toolDefaultValue?: any) => {
  handleOpenChange(false)
  emit('select', type, toolDefaultValue)
}

const handleSelect = () => {
  // 处理下拉菜单选择事件
}

// 监听外部 open 变化
watch(
  () => props.open,
  (newOpen) => {
    if (newOpen !== undefined) {
      localOpen.value = newOpen
    }
  }
)
</script>

<style scoped lang="scss">
.block-selector-panel {
  width: 320px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--color-border-2);
}

.search-section {
  padding: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.block-tabs {
  :deep(.arco-tabs-nav) {
    padding: 0 12px;
    margin-bottom: 0;
  }

  :deep(.arco-tabs-content) {
    padding: 0;
  }
}

.block-selector-trigger {
  color: var(--color-text-2);

  &:hover {
    color: var(--color-text-1);
    background-color: var(--color-fill-2);
  }
}
</style>
