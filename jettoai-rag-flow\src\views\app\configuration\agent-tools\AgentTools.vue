<template>
  <div>
    <FeaturePanel class="mt-2">
      <template #title>
        <div>
          工具
          <a-tooltip content="使用工具可以扩展代理的能力，比如搜索互联网或科学计算">
            <icon-question-circle />
          </a-tooltip>
        </div>
      </template>
      <template #headerRight>
        <div>
          <div class="flex items-center">
            <span class="text-sm font-normal leading-[18px] text-text-tertiary">
              {{ tools.filter((item) => !!item.enabled).length }} / {{ tools.length }}&nbsp; 启用
            </span>
            <template v-if="tools.length < 10">
              <a-divider direction="vertical" />
              <a-button size="small" @click="handleAddDataset">添加</a-button>
            </template>
          </div>
        </div>
      </template>
      <template #default>
        <div class="grid grid-cols-1 flex-wrap items-center justify-between gap-1 2xl:grid-cols-2">
          <div
            v-for="(item, index) in tools"
            :key="item.id"
            class="tool-item cursor group relative flex w-full items-center justify-between rounded-lg border-[0.5px] border-components-panel-border-subtle p-1.5 pr-2 shadow-xs hover:shadow-sm"
          >
            {{ item.name }}

            <div class="flex w-0 grow items-center">
              <img
                v-if="typeof item.icon === 'string'" :src="item.icon"
                style="width: 16px; height: 16px;display: inline-block" alt=""
              />
              <div v-else :style="{ backgroundColor: item.icon ? item.icon.background : '#ffffff' }">
                {{ item.icon?.content }}
              </div>
              <div class="system-xs-regular ml-1.5 flex w-0 grow items-center truncate">
                <div class="system-xs-medium ml-1.5 pr-1.5 text-text-secondary">
                  {{
                    item.provider_type === CollectionType.builtIn ? item.provider_name.split('/').pop() : item.tool_label
                  }}
                </div>
                <div class="text-text-tertiary ml-1.5">{{ item.tool_label }}</div>
              </div>
              <a-tooltip :content="'工具调用名称，用于 Agent 推理和提示词'">
                <template #content>
                  <div class="mb-1.5 text-text-secondary">{{ item.tool_name }}</div>
                  <div class="mb-1.5 text-text-tertiary">工具调用名称，用于 Agent 推理和提示词</div>
                  <!--<div>
                    <a-typography-paragraph copyable :copy-text="item.tool_name">
                      复制名称
                    </a-typography-paragraph>
                  </div>-->
                </template>
                <!--group-hover:inline-block hidden-->
                <icon-info-circle class="ml-1.5" />
              </a-tooltip>
            </div>

            <!--右侧-->
            <div class="ml-1 flex shrink-0 items-center">
              <div class="cursor-pointer rounded-md p-1 hover:bg-black/5">
                <a-tooltip :content="'信息和设置'">
                  <icon-settings />
                </a-tooltip>
              </div>
              <div class="cursor-pointer rounded-md p-1 hover:bg-black/5" @click="deleteTool(index)">
                <a-tooltip :content="'删除'">
                  <icon-delete />
                </a-tooltip>
              </div>
              <div class="ml-1.5">
                <a-switch v-model="item.enabled" size="small" type="round" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </FeaturePanel>
  </div>
</template>
<script setup lang="ts">
import FeaturePanel from '@/views/app/configuration/base/feature-panel/index.vue'
import { CollectionType } from '@/views/app/workflow/types/variable'
import { canFindTool } from '@/views/app/workflow/utils/configuration'

const props = defineProps(['collectionList', 'selectedTools'])
const handleAddDataset = () => {

}

/**
 * enabled:true
 * isDeleted:false
 * notAuthor:false
 * provider_id:"code"
 * provider_name:"code"
 * provider_type:"builtin"
 * tool_label:"代码解释器"
 * tool_name:"simple_code"
 * tool_parameters:{language: '', code: ''}
 */
const tools = ref([])

onMounted(() => {

})
const deleteTool = (index) => {
  tools.value.splice(index, 1)
}
watch([() => props.selectedTools, () => props.collectionList], () => {
  tools.value = props.selectedTools.map((item: any) => {
    const collection = props.collectionList.find(
      (collection: any) =>
        canFindTool(collection.id, item.provider_id)
        && collection.type === item.provider_type
    )
    const icon = collection?.icon
    return {
      ...item,
      icon,
      collection
    }
  })
  console.log('watch:', tools.value)
})
</script>

<style scoped lang="scss">
.tool-item {
  background: #ffffff;
}
</style>
