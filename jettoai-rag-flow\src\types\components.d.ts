/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AiCodeMirror: typeof import('./../components/AiCodeMirror/index.vue')['default']
    AiEditor: typeof import('./../components/AiEditor/index.vue')['default']
    AiEditTable: typeof import('./../components/AiEditTable/AiEditTable.vue')['default']
    AiFileUpload: typeof import('./../components/AiFileUpload/index.vue')['default']
    AiForm: typeof import('./../components/AiForm/src/AiForm.vue')['default']
    AiIconSelector: typeof import('./../components/AiIconSelector/index.vue')['default']
    AiIframe: typeof import('./../components/AiIframe/index.vue')['default']
    AiMarkdown: typeof import('./../components/AiMarkdown/index.vue')['default']
    AiPageLayout: typeof import('./../components/AiPageLayout/index.vue')['default']
    AiSvgIcon: typeof import('./../components/AiSvgIcon/index.vue')['default']
    AiTable: typeof import('./../components/AiTable/src/AiTable.vue')['default']
    AiThemeBtn: typeof import('./../components/AiThemeBtn/index.vue')['default']
    Avatar: typeof import('./../components/Avatar/index.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    CellCopy: typeof import('./../components/CellCopy/index.vue')['default']
    ColumnSetting: typeof import('./../components/AiTable/src/components/ColumnSetting.vue')['default']
    DateRangePicker: typeof import('./../components/DateRangePicker/index.vue')['default']
    FilePreview: typeof import('./../components/FilePreview/index.vue')['default']
    Icon403: typeof import('./../components/icons/Icon403.vue')['default']
    Icon404: typeof import('./../components/icons/Icon404.vue')['default']
    Icon500: typeof import('./../components/icons/Icon500.vue')['default']
    IconBorders: typeof import('./../components/icons/IconBorders.vue')['default']
    IconTableSize: typeof import('./../components/icons/IconTableSize.vue')['default']
    IconTreeAdd: typeof import('./../components/icons/IconTreeAdd.vue')['default']
    IconTreeReduce: typeof import('./../components/icons/IconTreeReduce.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TextCopy: typeof import('./../components/TextCopy/index.vue')['default']
  }
}
