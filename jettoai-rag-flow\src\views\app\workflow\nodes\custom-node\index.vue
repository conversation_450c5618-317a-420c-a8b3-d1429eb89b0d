<template>
  <div class="custom-node" :style="{ width: data.width, height: data.height }">
    <div class="truncate flex items-center p-1">
      <div class="custom-node-icon">
        <img src="../../../../../assets/icons/workflow/note.svg" alt="" />
      </div>
      <div class="custom-node-text">{{ data.title }}</div>
    </div>
    <div class="input-note">
      <!-- 使用富文本编辑器替代简单输入框 -->
      <div class="text-editor-container">
        <div
          ref="textEditorRef"
          class="text-editor nodrag nopan nowheel"
          contenteditable="true"
          :data-placeholder="placeholder"
          @input="handleTextInput"
          @blur="handleTextBlur"
          @focus="handleTextFocus"
          @keydown="handleKeyDown"
          @paste="handlePaste"
        />
      </div>
      <a-input v-model="data.author" :style="{ height: '40px' }" disabled placeholder="作者" allow-clear />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import { useVueFlow } from '@vue-flow/core'

const props = defineProps<NodeProps>()
const { updateNode } = useVueFlow()

// 编辑器引用
const textEditorRef = ref<HTMLDivElement | null>(null)

// 占位符文本
const placeholder = ref('在此添加注释内容...')

// 编辑器状态
const isEditing = ref(false)
const rawTextContent = ref('')

// 初始化文本内容
const initializeTextContent = () => {
  try {
    // 尝试解析 JSON 格式的编辑器状态（参考 dify）
    if (props.data.text) {
      const editorState = JSON.parse(props.data.text)
      // 如果是 JSON 格式，提取纯文本内容
      if (editorState && editorState.root && editorState.root.children) {
        rawTextContent.value = extractTextFromEditorState(editorState)
      } else {
        // 如果不是标准格式，直接使用文本
        rawTextContent.value = props.data.text
      }
    } else {
      rawTextContent.value = ''
    }
  } catch {
    // JSON 解析失败，直接使用原始文本
    rawTextContent.value = props.data.text || ''
  }
}

// 从编辑器状态中提取纯文本（简化版本）
const extractTextFromEditorState = (editorState: any): string => {
  if (!editorState.root || !editorState.root.children) return ''

  let text = ''
  const extractFromChildren = (children: any[]) => {
    children.forEach((child: any) => {
      if (child.type === 'text') {
        text += child.text || ''
      } else if (child.children) {
        extractFromChildren(child.children)
      }
    })
  }

  extractFromChildren(editorState.root.children)
  return text
}

// 移除 displayText，直接操作 DOM

// 处理文本输入
const handleTextInput = (event: Event) => {
  const target = event.target as HTMLDivElement
  const newText = target.innerText || target.textContent || ''
  rawTextContent.value = newText

  // 实时更新节点数据（参考 dify 的实时更新）
  updateNodeText(newText)
}

// 处理文本失焦
const handleTextBlur = () => {
  isEditing.value = false
  const finalText = rawTextContent.value.trim()

  // 失焦时保存最终状态（参考 dify 的保存机制）
  saveTextAsEditorState(finalText)
}

// 处理文本聚焦
const handleTextFocus = () => {
  isEditing.value = true

  // 聚焦时清除占位符样式
  nextTick(() => {
    if (textEditorRef.value && !rawTextContent.value) {
      textEditorRef.value.textContent = ''
    }
  })
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 阻止某些快捷键被 Vue Flow 拦截
  event.stopPropagation()

  // 处理回车键
  if (event.key === 'Enter') {
    // 允许换行
    return true
  }

  // 处理其他快捷键
  if (event.ctrlKey || event.metaKey) {
    // 允许复制粘贴等操作
    return true
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  event.stopPropagation()

  // 获取粘贴的纯文本
  const text = event.clipboardData?.getData('text/plain') || ''

  // 插入文本到当前光标位置
  if (textEditorRef.value) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.deleteContents()
      range.insertNode(document.createTextNode(text))
      range.collapse(false)
      selection.removeAllRanges()
      selection.addRange(range)
    }

    // 触发输入事件
    const inputEvent = new Event('input', { bubbles: true })
    textEditorRef.value.dispatchEvent(inputEvent)
  }
}

// 更新节点文本数据
const updateNodeText = (text: string) => {
  // 创建简化的编辑器状态格式（参考 dify）
  const editorState = createSimpleEditorState(text)

  // 更新节点数据（使用 VueFlow 的 updateNode）
  updateNode(props.id, {
    data: {
      ...props.data,
      text: JSON.stringify(editorState)
    }
  })
}

// 保存文本为编辑器状态格式
const saveTextAsEditorState = (text: string) => {
  const editorState = createSimpleEditorState(text)

  // 更新节点数据（参考 dify 的保存方式）
  updateNode(props.id, {
    data: {
      ...props.data,
      text: text ? JSON.stringify(editorState) : ''
    }
  })
}

// 创建简化的编辑器状态（参考 dify 的 EditorState 结构）
const createSimpleEditorState = (text: string) => {
  if (!text.trim()) {
    return {
      root: {
        children: [],
        direction: null,
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    }
  }

  // 处理多行文本
  const lines = text.split('\n')
  const children = lines.map((line) => ({
    children: line
      ? [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: line,
            type: 'text',
            version: 1
          }
        ]
      : [],
    direction: null,
    format: '',
    indent: 0,
    type: 'paragraph',
    version: 1
  }))

  return {
    root: {
      children,
      direction: null,
      format: '',
      indent: 0,
      type: 'root',
      version: 1
    }
  }
}

// 更新编辑器显示内容
const updateEditorDisplay = () => {
  if (textEditorRef.value) {
    textEditorRef.value.textContent = rawTextContent.value
  }
}

// 监听 props.data.text 变化
watch(
  () => props.data.text,
  () => {
    initializeTextContent()
    nextTick(() => {
      updateEditorDisplay()
    })
  },
  { immediate: true }
)

// 监听 rawTextContent 变化，更新编辑器显示
watch(
  () => rawTextContent.value,
  () => {
    if (!isEditing.value) {
      nextTick(() => {
        updateEditorDisplay()
      })
    }
  }
)

// 组件挂载时初始化
initializeTextContent()
</script>
<style scoped lang="scss">
.custom-node {
  min-width: 240px;
  padding: 8px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-icon {
    margin-right: 8px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: 0;
    pointer-events: none;
  }
}
.input-note {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

// 文本编辑器样式（参考 dify 的编辑器样式）
.text-editor-container {
  width: 100%;
  min-height: 60px;
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  background-color: var(--color-bg-1);
  transition: border-color 0.2s ease;

  &:hover {
    border-color: var(--color-border-3);
  }

  &:focus-within {
    border-color: rgb(var(--primary-6));
    box-shadow: 0 0 0 2px rgba(var(--primary-6), 0.1);
  }
}

.text-editor {
  width: 100%;
  min-height: 58px;
  padding: 8px 12px;
  border: none;
  outline: none;
  background: transparent;
  font-size: 13px;
  line-height: 1.5;
  color: var(--color-text-1);
  resize: none;
  overflow-y: auto;

  // 占位符样式
  .placeholder-text {
    color: var(--color-text-3);
    font-style: italic;
    pointer-events: none;
  }

  // 聚焦时的样式
  &:focus {
    outline: none;
  }

  // 空状态时显示占位符
  &:empty::before {
    content: attr(data-placeholder);
    color: var(--color-text-3);
    font-style: italic;
    pointer-events: none;
    display: block;
  }

  // 禁用拖拽时的样式
  &:focus {
    cursor: text;
  }

  // 处理换行
  br {
    line-height: 1.5;
  }

  // 处理段落间距
  p {
    margin: 0;
    line-height: 1.5;

    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}
</style>
