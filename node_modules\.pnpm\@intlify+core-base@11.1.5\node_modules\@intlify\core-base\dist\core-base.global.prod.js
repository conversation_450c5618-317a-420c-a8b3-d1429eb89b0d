/*!
  * core-base v11.1.5
  * (c) 2025 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
var IntlifyCoreBase=function(e){"use strict";const t=/\{([0-9a-zA-Z]+)\}/g;const n=(e,t,n)=>r({l:e,k:t,s:n}),r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),c=e=>"[object Date]"===C(e),s=e=>"[object RegExp]"===C(e),a=e=>k(e)&&0===Object.keys(e).length,l=Object.assign,i=Object.create,u=(e=null)=>i(e);function f(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const E=Object.prototype.hasOwnProperty;function _(e,t){return E.call(e,t)}const m=Array.isArray,d=e=>"function"==typeof e,p=e=>"string"==typeof e,N=e=>"boolean"==typeof e,L=e=>null!==e&&"object"==typeof e,T=e=>L(e)&&d(e.then)&&d(e.catch),A=Object.prototype.toString,C=e=>A.call(e),k=e=>"[object Object]"===C(e);function h(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function O(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}function I(e,t,n){return{start:e,end:t}}const g={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16};function S(e,t,n={}){const{domain:r,messages:o,args:c}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function y(e){throw e}g.EXPECTED_TOKEN,g.INVALID_TOKEN_IN_PLACEHOLDER,g.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,g.UNKNOWN_ESCAPE_SEQUENCE,g.INVALID_UNICODE_ESCAPE_SEQUENCE,g.UNBALANCED_CLOSING_BRACE,g.UNTERMINATED_CLOSING_BRACE,g.EMPTY_PLACEHOLDER,g.NOT_ALLOW_NEST_PLACEHOLDER,g.INVALID_LINKED_FORMAT,g.MUST_HAVE_MESSAGES_IN_PLURAL,g.UNEXPECTED_EMPTY_LINKED_MODIFIER,g.UNEXPECTED_EMPTY_LINKED_KEY,g.UNEXPECTED_LEXICAL_ANALYSIS,g.UNHANDLED_CODEGEN_NODE_TYPE,g.UNHANDLED_MINIFIER_NODE_TYPE;const b=" ",D="\r",P="\n",R=String.fromCharCode(8232),M=String.fromCharCode(8233);function U(e){const t=e;let n=0,r=1,o=1,c=0;const s=e=>t[e]===D&&t[e+1]===P,a=e=>t[e]===M,l=e=>t[e]===R,i=e=>s(e)||(e=>t[e]===P)(e)||a(e)||l(e),u=e=>s(e)||a(e)||l(e)?P:t[e];function f(){return c=0,i(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>c,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+c),next:f,peek:function(){return s(n+c)&&c++,c++,t[n+c]},reset:function(){n=0,r=1,o=1,c=0},resetPeek:function(e=0){c=e},skipToPeek:function(){const e=n+c;for(;e!==n;)f();c=0}}}const v=void 0,x="'";function F(e,t={}){const n=!1!==t.location,r=U(e),o=()=>r.index(),c=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=c(),a=o(),l={currentType:13,offset:a,startLoc:s,endLoc:s,lastType:13,lastOffset:a,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},i=()=>l,{onError:u}=t;function f(e,t,r){e.endLoc=c(),e.currentType=t;const o={type:t};return n&&(o.loc=I(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const E=e=>f(e,13);function _(e,t){return e.currentChar()===t?(e.next(),t):(g.EXPECTED_TOKEN,c(),"")}function m(e){let t="";for(;e.currentPeek()===b||e.currentPeek()===P;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=m(e);return e.skipToPeek(),t}function p(e){if(e===v)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function N(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=function(e){if(e===v)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function L(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function T(e,t=!0){const n=(t=!1,r="")=>{const o=e.currentPeek();return"{"===o?t:"@"!==o&&o?"|"===o?!(r===b||r===P):o===b?(e.peek(),n(!0,b)):o!==P||(e.peek(),n(!0,P)):t},r=n();return t&&e.resetPeek(),r}function A(e,t){const n=e.currentChar();return n===v?v:t(n)?(e.next(),n):null}function C(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function k(e){return A(e,C)}function h(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function O(e){return A(e,h)}function S(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function y(e){return A(e,S)}function D(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function R(e){return A(e,D)}function M(e){let t="",n="";for(;t=y(e);)n+=t;return n}function F(e){return e!==x&&e!==P}function w(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return W(e,t,4);case"U":return W(e,t,6);default:return g.UNKNOWN_ESCAPE_SEQUENCE,c(),""}}function W(e,t,n){_(e,t);let r="";for(let o=0;o<n;o++){const t=R(e);if(!t){g.INVALID_UNICODE_ESCAPE_SEQUENCE,c(),e.currentChar();break}r+=t}return`\\${t}${r}`}function Y(e){return"{"!==e&&"}"!==e&&e!==b&&e!==P}function K(e){d(e);const t=_(e,"|");return d(e),t}function G(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(g.NOT_ALLOW_NEST_PLACEHOLDER,c()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(g.EMPTY_PLACEHOLDER,c()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(g.UNTERMINATED_CLOSING_BRACE,c()),n=X(e,t)||E(t),t.braceNest=0,n;default:{let r=!0,o=!0,s=!0;if(L(e))return t.braceNest>0&&(g.UNTERMINATED_CLOSING_BRACE,c()),n=f(t,1,K(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return g.UNTERMINATED_CLOSING_BRACE,c(),t.braceNest=0,$(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=p(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,4,function(e){d(e);let t="",n="";for(;t=O(e);)n+=t;return e.currentChar()===v&&(g.UNTERMINATED_CLOSING_BRACE,c()),n}(e)),d(e),n;if(o=N(e,t))return n=f(t,5,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${M(e)}`):t+=M(e),e.currentChar()===v&&(g.UNTERMINATED_CLOSING_BRACE,c()),t}(e)),d(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=e.currentPeek()===x;return e.resetPeek(),r}(e,t))return n=f(t,6,function(e){d(e),_(e,"'");let t="",n="";for(;t=A(e,F);)n+="\\"===t?w(e):t;const r=e.currentChar();return r===P||r===v?(g.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,c(),r===P&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),d(e),n;if(!r&&!o&&!s)return n=f(t,12,function(e){d(e);let t="",n="";for(;t=A(e,Y);)n+=t;return n}(e)),g.INVALID_TOKEN_IN_PLACEHOLDER,c(),n.value,d(e),n;break}}return n}function X(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||o!==P&&o!==b||(g.INVALID_LINKED_FORMAT,c()),o){case"@":return e.next(),r=f(t,7,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,8,".");case":":return d(e),e.next(),f(t,9,":");default:return L(e)?(r=f(t,1,K(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),X(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const r=p(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,11,function(e){let t="",n="";for(;t=k(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?p(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===b||!t)&&(t===P?(e.peek(),r()):T(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(d(e),"{"===o?G(e,t)||r:f(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===b?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(g.INVALID_LINKED_FORMAT,c()),t.braceNest=0,t.inLinked=!1,$(e,t))}}function $(e,t){let n={type:13};if(t.braceNest>0)return G(e,t)||E(t);if(t.inLinked)return X(e,t)||E(t);switch(e.currentChar()){case"{":return G(e,t)||E(t);case"}":return g.UNBALANCED_CLOSING_BRACE,c(),e.next(),f(t,3,"}");case"@":return X(e,t)||E(t);default:if(L(e))return n=f(t,1,K(e)),t.braceNest=0,t.inLinked=!1,n;if(T(e))return f(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===b||n===P)if(T(e))t+=n,e.next();else{if(L(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=s,l.offset=o(),l.startLoc=c(),r.currentChar()===v?f(l,13):$(r,l)},currentOffset:o,currentPosition:c,context:i}}const w=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function W(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function Y(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function o(e,n,r,o){t&&(e.end=n,e.loc&&(e.loc.end=r))}function c(e,t){const n=e.context(),c=r(3,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}function s(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(5,c,s);return a.index=parseInt(t,10),e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function a(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(4,c,s);return a.key=t,e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function i(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(9,c,s);return a.value=t.replace(w,W),e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let c=e.nextToken();if(8===c.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(8,c,s);return 11!==t.type?(g.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,a.value="",o(a,c,s),{nextConsumeToken:t,node:a}):(null==t.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,K(t)),a.value=t.value||"",o(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,c=t.nextConsumeToken||e.nextToken()}switch(9!==c.type&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(c)),c=e.nextToken(),2===c.type&&(c=e.nextToken()),c.type){case 10:null==c.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(c)),n.key=function(e,t){const n=e.context(),c=r(7,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}(e,c.value||"");break;case 4:null==c.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(c)),n.key=a(e,c.value||"");break;case 5:null==c.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(c)),n.key=s(e,c.value||"");break;case 6:null==c.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(c)),n.key=i(e,c.value||"");break;default:{g.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),a=r(7,s.offset,s.startLoc);return a.value="",o(a,s.offset,s.startLoc),n.key=a,o(n,s.offset,s.startLoc),{nextConsumeToken:c,node:n}}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let l=null;do{const r=l||e.nextToken();switch(l=null,r.type){case 0:null==r.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.items.push(c(e,r.value||""));break;case 5:null==r.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.items.push(s(e,r.value||""));break;case 4:null==r.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.items.push(a(e,r.value||""));break;case 6:null==r.value&&(g.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.items.push(i(e,r.value||""));break;case 7:{const t=u(e);n.items.push(t.node),l=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function E(e){const t=e.context(),{offset:n,startLoc:c}=t,s=f(e);return 13===t.currentType?s:function(e,t,n,c){const s=e.context();let a=0===c.items.length;const l=r(1,t,n);l.cases=[],l.cases.push(c);do{const t=f(e);a||(a=0===t.items.length),l.cases.push(t)}while(13!==s.currentType);return a&&g.MUST_HAVE_MESSAGES_IN_PLURAL,o(l,e.currentOffset(),e.currentPosition()),l}(e,n,c,s)}return{parse:function(n){const c=F(n,l({},e)),s=c.context(),a=r(0,s.offset,s.startLoc);return t&&a.loc&&(a.loc.source=n),a.body=E(c),e.onCacheKey&&(a.cacheKey=e.onCacheKey(n)),13!==s.currentType&&(g.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),o(a,c.currentOffset(),c.currentPosition()),a}}}function K(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function G(e,t){for(let n=0;n<e.length;n++)X(e[n],t)}function X(e,t){switch(e.type){case 1:G(e.cases,t),t.helper("plural");break;case 2:G(e.items,t);break;case 6:X(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function $(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&X(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function V(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=h(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function H(e){switch(e.t=e.type,e.type){case 0:{const t=e;H(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)H(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)H(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;H(t.key),t.k=t.key,delete t.key,t.modifier&&(H(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function B(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?B(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(B(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let c=0;c<o&&(B(e,t.items[c]),c!==o-1);c++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),B(e,t.key),t.modifier?(e.push(", "),B(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const j=(e,t={})=>{const n=p(t.mode)?t.mode:"normal",r=p(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,c=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,a=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:c}=t,s=!1!==t.location,a={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:c,indentLevel:0};function l(e,t){a.code+=e}function i(e,t=!0){const n=t?o:"";l(c?n+"  ".repeat(e):n)}return s&&e.loc&&(a.source=e.loc.source),{context:()=>a,push:l,indent:function(e=!0){const t=++a.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--a.indentLevel;e&&i(t)},newline:function(){i(a.indentLevel)},helper:e=>`_${e}`,needIndent:()=>a.needIndent}}(e,{mode:n,filename:r,sourceMap:o,breakLineCode:c,needIndent:s});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(s),a.length>0&&(l.push(`const { ${h(a.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),B(l,e),l.deindent(s),l.push("}"),delete e.helpers;const{code:i,map:u}=l.context();return{ast:e,code:i,map:u?u.toJSON():void 0}};function z(e,t={}){const n=l({},t),r=!!n.jit,o=!!n.minify,c=null==n.optimize||n.optimize,s=Y(n).parse(e);return r?(c&&function(e){const t=e.body;2===t.type?V(t):t.cases.forEach((e=>V(e)))}(s),o&&H(s),{ast:s,code:""}):($(s,n),j(s,n))}function Q(e){return L(e)&&0===ne(e)&&(_(e,"b")||_(e,"body"))}const J=["b","body"];const Z=["c","cases"];const q=["s","static"];const ee=["i","items"];const te=["t","type"];function ne(e){return ae(e,te)}const re=["v","value"];function oe(e,t){const n=ae(e,re);if(null!=n)return n;throw ie(t)}const ce=["m","modifier"];const se=["k","key"];function ae(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(_(e,n)&&null!=e[n])return e[n]}return n}const le=[...J,...Z,...q,...ee,...se,...ce,...re,...te];function ie(e){return new Error(`unhandled node type: ${e}`)}function ue(e){return t=>function(e,t){const n=(r=t,ae(r,J));var r;if(null==n)throw ie(0);if(1===ne(n)){const t=function(e){return ae(e,Z,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,fe(e,n)]),[]))}return fe(e,n)}(t,e)}function fe(e,t){const n=function(e){return ae(e,q)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return ae(e,ee,[])}(t).reduce(((t,n)=>[...t,Ee(e,n)]),[]);return e.normalize(n)}}function Ee(e,t){const n=ne(t);switch(n){case 3:case 9:case 7:case 8:return oe(t,n);case 4:{const r=t;if(_(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(_(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw ie(n)}case 5:{const r=t;if(_(r,"i")&&o(r.i))return e.interpolate(e.list(r.i));if(_(r,"index")&&o(r.index))return e.interpolate(e.list(r.index));throw ie(n)}case 6:{const n=t,r=function(e){return ae(e,ce)}(n),o=function(e){const t=ae(e,se);if(t)return t;throw ie(6)}(n);return e.linked(Ee(e,o),r?Ee(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const _e=e=>e;let me=u();let de=null;const pe=Ne("function:translate");function Ne(e){return t=>de&&de.emit(e,t)}const Le={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23};function Te(e,t){return null!=t.locale?Ce(t.locale):Ce(e.locale)}let Ae;function Ce(e){if(p(e))return e;if(d(e)){if(e.resolvedOnce&&null!=Ae)return Ae;if("Function"===e.constructor.name){const t=e();if(T(t))throw Error(Le.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Ae=t}throw Error(Le.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(Le.NOT_SUPPORT_LOCALE_TYPE)}function ke(e,t,n){return[...new Set([n,...m(t)?t:L(t)?Object.keys(t):p(t)?[t]:[n]])]}function he(e,t,n){let r=!0;for(let o=0;o<t.length&&N(r);o++){const c=t[o];p(c)&&(r=Oe(e,t[o],n))}return r}function Oe(e,t,n){let r;const o=t.split("-");do{r=Ie(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function Ie(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(m(n)||k(n))&&n[o]&&(r=n[o])}return r}Le.INVALID_ARGUMENT,Le.INVALID_DATE_ARGUMENT,Le.INVALID_ISO_DATE_ARGUMENT,Le.NOT_SUPPORT_NON_STRING_MESSAGE,Le.NOT_SUPPORT_LOCALE_PROMISE_VALUE,Le.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,Le.NOT_SUPPORT_LOCALE_TYPE;const ge=[];ge[0]={w:[0],i:[3,0],"[":[4],o:[7]},ge[1]={w:[1],".":[2],"[":[4],o:[7]},ge[2]={w:[2],i:[3,0],0:[3,0]},ge[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},ge[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},ge[5]={"'":[4,0],o:8,l:[5,0]},ge[6]={'"':[4,0],o:8,l:[6,0]};const Se=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ye(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function be(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Se.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}function De(e){const t=[];let n,r,o,c,s,a,l,i=-1,u=0,f=0;const E=[];function _(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,o="\\"+t,E[0](),!0}for(E[0]=()=>{void 0===r?r=o:r+=o},E[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},E[2]=()=>{E[0](),f++},E[3]=()=>{if(f>0)f--,u=4,E[0]();else{if(f=0,void 0===r)return!1;if(r=be(r),!1===r)return!1;E[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!_()){if(c=ye(n),l=ge[u],s=l[c]||l.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(a=E[s[1]],a&&(o=n,!1===a())))return;if(7===u)return t}}const Pe=new Map;function Re(e,t){return L(e)?e[t]:null}const Me={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:7},Ue={[Me.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[Me.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[Me.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[Me.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[Me.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[Me.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[Me.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};const ve="11.1.5",xe="en-US",Fe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let we,We,Ye;let Ke=null;let Ge=null;let Xe=0;const $e=e=>({[e]:u()});function Ve(e,t,n,r,o){const{missing:c,onWarn:s}=e;if(null!==c){const r=c(e,n,t,o);return p(r)?r:t}return t}function He(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function Be(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(He(e,t[r]))return!0;return!1}const je=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ze(...e){const[t,n,r,s]=e,a=u();let l,i=u();if(p(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Le.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(n);try{l.toISOString()}catch{throw Error(Le.INVALID_ISO_DATE_ARGUMENT)}}else if(c(t)){if(isNaN(t.getTime()))throw Error(Le.INVALID_DATE_ARGUMENT);l=t}else{if(!o(t))throw Error(Le.INVALID_ARGUMENT);l=t}return p(n)?a.key=n:k(n)&&Object.keys(n).forEach((e=>{je.includes(e)?i[e]=n[e]:a[e]=n[e]})),p(r)?a.locale=r:k(r)&&(i=r),k(s)&&(i=s),[a.key||"",l,a,i]}const Qe=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Je(...e){const[t,n,r,c]=e,s=u();let a=u();if(!o(t))throw Error(Le.INVALID_ARGUMENT);const l=t;return p(n)?s.key=n:k(n)&&Object.keys(n).forEach((e=>{Qe.includes(e)?a[e]=n[e]:s[e]=n[e]})),p(r)?s.locale=r:k(r)&&(a=r),k(c)&&(a=c),[s.key||"",l,s,a]}const Ze=e=>e,qe=e=>"",et="text",tt=e=>0===e.length?"":h(e),nt=e=>null==e?"":m(e)||k(e)&&e.toString===A?JSON.stringify(e,null,2):String(e);function rt(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function ot(e={}){const t=e.locale,n=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),r=L(e.pluralRules)&&p(t)&&d(e.pluralRules[t])?e.pluralRules[t]:rt,c=L(e.pluralRules)&&p(t)&&d(e.pluralRules[t])?rt:void 0,s=e.list||[],a=e.named||u();o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,a);function i(t,n){const r=d(e.messages)?e.messages(t,!!n):!!L(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):qe)}const f=k(e.processor)&&d(e.processor.normalize)?e.processor.normalize:tt,E=k(e.processor)&&d(e.processor.interpolate)?e.processor.interpolate:nt,_={list:e=>s[e],named:e=>a[e],plural:e=>e[r(n,e.length,c)],linked:(t,...n)=>{const[r,o]=n;let c="text",s="";1===n.length?L(r)?(s=r.modifier||s,c=r.type||c):p(r)&&(s=r||s):2===n.length&&(p(r)&&(s=r||s),p(o)&&(c=o||c));const a=i(t,!0)(_),l="vnode"===c&&m(a)&&s?a[0]:a;return s?(u=s,e.modifiers?e.modifiers[u]:Ze)(l,c):l;var u},message:i,type:k(e.processor)&&p(e.processor.type)?e.processor.type:et,interpolate:E,normalize:f,values:l(u(),s,a)};return _}const ct=()=>"",st=e=>d(e);function at(e,t,n,r,o,c){const{messages:s,onWarn:a,messageResolver:l,localeFallbacker:i}=e,f=i(e,r,n);let E,_=u(),m=null;for(let d=0;d<f.length&&(E=f[d],_=s[E]||u(),null===(m=l(_,t))&&(m=_[t]),!(p(m)||Q(m)||st(m)));d++)if(!Be(E,f)){const n=Ve(e,t,E,0,"translate");n!==t&&(m=n)}return[m,E,_]}function lt(e,t,r,o,c,s){const{messageCompiler:a,warnHtmlMessage:l}=e;if(st(o)){const e=o;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==a){const e=()=>o;return e.locale=r,e.key=t,e}const i=a(o,function(e,t,r,o,c,s){return{locale:t,key:r,warnHtmlMessage:c,onError:e=>{throw s&&s(e),e},onCacheKey:e=>n(t,r,e)}}(0,r,c,0,l,s));return i.locale=r,i.key=t,i.source=o,i}function it(...e){const[t,n,r]=e,c=u();if(!(p(t)||o(t)||st(t)||Q(t)))throw Error(Le.INVALID_ARGUMENT);const s=o(t)?String(t):(st(t),t);return o(n)?c.plural=n:p(n)?c.default=n:k(n)&&!a(n)?c.named=n:m(n)&&(c.list=n),o(r)?c.plural=r:p(r)?c.default=r:k(r)&&l(c,r),[s,c]}return e.AST_NODE_PROPS_KEYS=le,e.CORE_ERROR_CODES_EXTEND_POINT=24,e.CORE_WARN_CODES_EXTEND_POINT=8,e.CompileErrorCodes=g,e.CoreErrorCodes=Le,e.CoreWarnCodes=Me,e.DATETIME_FORMAT_OPTIONS_KEYS=je,e.DEFAULT_LOCALE=xe,e.DEFAULT_MESSAGE_DATA_TYPE=et,e.MISSING_RESOLVE_VALUE="",e.NOT_REOSLVED=-1,e.NUMBER_FORMAT_OPTIONS_KEYS=Qe,e.VERSION=ve,e.clearCompileCache=function(){me=u()},e.clearDateTimeFormat=function(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}},e.clearNumberFormat=function(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}},e.compile=function(e,t){if(p(e)){!N(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||_e)(e),r=me[n];if(r)return r;const{ast:o,detectError:c}=function(e,t={}){let n=!1;const r=t.onError||y;return t.onError=e=>{n=!0,r(e)},{...z(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),s=ue(o);return c?s:me[n]=s}{const t=e.cacheKey;if(t){const n=me[t];return n||(me[t]=ue(e))}return ue(e)}},e.createCompileError=S,e.createCoreContext=function(e={}){const t=d(e.onWarn)?e.onWarn:O,n=p(e.version)?e.version:ve,r=p(e.locale)||d(e.locale)?e.locale:xe,o=d(r)?xe:r,c=m(e.fallbackLocale)||k(e.fallbackLocale)||p(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,a=k(e.messages)?e.messages:$e(o),i=k(e.datetimeFormats)?e.datetimeFormats:$e(o),f=k(e.numberFormats)?e.numberFormats:$e(o),E=l(u(),e.modifiers,{upper:(e,t)=>"text"===t&&p(e)?e.toUpperCase():"vnode"===t&&L(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&p(e)?e.toLowerCase():"vnode"===t&&L(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&p(e)?Fe(e):"vnode"===t&&L(e)&&"__v_isVNode"in e?Fe(e.children):e}),_=e.pluralRules||u(),T=d(e.missing)?e.missing:null,A=!N(e.missingWarn)&&!s(e.missingWarn)||e.missingWarn,C=!N(e.fallbackWarn)&&!s(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,I=!!e.unresolving,g=d(e.postTranslation)?e.postTranslation:null,S=k(e.processor)?e.processor:null,y=!N(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter,D=d(e.messageCompiler)?e.messageCompiler:we,P=d(e.messageResolver)?e.messageResolver:We||Re,R=d(e.localeFallbacker)?e.localeFallbacker:Ye||ke,M=L(e.fallbackContext)?e.fallbackContext:void 0,U=e,v=L(U.__datetimeFormatters)?U.__datetimeFormatters:new Map,x=L(U.__numberFormatters)?U.__numberFormatters:new Map,F=L(U.__meta)?U.__meta:{};Xe++;const w={version:n,cid:Xe,locale:r,fallbackLocale:c,messages:a,modifiers:E,pluralRules:_,missing:T,missingWarn:A,fallbackWarn:C,fallbackFormat:h,unresolving:I,postTranslation:g,processor:S,warnHtmlMessage:y,escapeParameter:b,messageCompiler:D,messageResolver:P,localeFallbacker:R,fallbackContext:M,onWarn:t,__meta:F};return w.datetimeFormats=i,w.numberFormats=f,w.__datetimeFormatters=v,w.__numberFormatters=x,w},e.createCoreError=function(e){return S(e,null,void 0)},e.createMessageContext=ot,e.datetime=function(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:c,localeFallbacker:s}=e,{__datetimeFormatters:i}=e,[u,f,E,_]=ze(...t);N(E.missingWarn)?E.missingWarn:e.missingWarn,N(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn;const m=!!E.part,d=Te(e,E),L=s(e,o,d);if(!p(u)||""===u)return new Intl.DateTimeFormat(d,_).format(f);let T,A={},C=null;for(let a=0;a<L.length&&(T=L[a],A=n[T]||{},C=A[u],!k(C));a++)Ve(e,u,T,0,"datetime format");if(!k(C)||!p(T))return r?-1:u;let h=`${T}__${u}`;a(_)||(h=`${h}__${JSON.stringify(_)}`);let O=i.get(h);return O||(O=new Intl.DateTimeFormat(T,l({},C,_)),i.set(h,O)),m?O.formatToParts(f):O.format(f)},e.fallbackWithLocaleChain=function(e,t,n){const r=p(n)?n:xe,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let c=o.__localeChainCache.get(r);if(!c){c=[];let e=[n];for(;m(e);)e=he(c,e,t);const s=m(t)||!k(t)?t:t.default?t.default:null;e=p(s)?[s]:s,m(e)&&he(c,e,!1),o.__localeChainCache.set(r,c)}return c},e.fallbackWithSimple=ke,e.getAdditionalMeta=()=>Ke,e.getDevToolsHook=function(){return de},e.getFallbackContext=()=>Ge,e.getLocale=Te,e.getWarnMessage=function(e,...n){return function(e,...n){return 1===n.length&&L(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),e.replace(t,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(Ue[e],...n)},e.handleMissing=Ve,e.initI18nDevTools=function(e,t,n){de&&de.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})},e.isAlmostSameLocale=He,e.isImplicitFallback=Be,e.isMessageAST=Q,e.isMessageFunction=st,e.isTranslateFallbackWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.isTranslateMissingWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.number=function(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:c,localeFallbacker:s}=e,{__numberFormatters:i}=e,[u,f,E,_]=Je(...t);N(E.missingWarn)?E.missingWarn:e.missingWarn,N(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn;const m=!!E.part,d=Te(e,E),L=s(e,o,d);if(!p(u)||""===u)return new Intl.NumberFormat(d,_).format(f);let T,A={},C=null;for(let a=0;a<L.length&&(T=L[a],A=n[T]||{},C=A[u],!k(C));a++)Ve(e,u,T,0,"number format");if(!k(C)||!p(T))return r?-1:u;let h=`${T}__${u}`;a(_)||(h=`${h}__${JSON.stringify(_)}`);let O=i.get(h);return O||(O=new Intl.NumberFormat(T,l({},C,_)),i.set(h,O)),m?O.formatToParts(f):O.format(f)},e.parse=De,e.parseDateTimeArgs=ze,e.parseNumberArgs=Je,e.parseTranslateArgs=it,e.registerLocaleFallbacker=function(e){Ye=e},e.registerMessageCompiler=function(e){we=e},e.registerMessageResolver=function(e){We=e},e.resolveLocale=Ce,e.resolveValue=function(e,t){if(!L(e))return null;let n=Pe.get(t);if(n||(n=De(t),n&&Pe.set(t,n)),!n)return null;const r=n.length;let o=e,c=0;for(;c<r;){const e=n[c];if(le.includes(e)&&Q(o))return null;const t=o[e];if(void 0===t)return null;if(d(o))return null;o=t,c++}return o},e.resolveWithKeyValue=Re,e.setAdditionalMeta=e=>{Ke=e},e.setDevToolsHook=function(e){de=e},e.setFallbackContext=e=>{Ge=e},e.translate=function(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:c,messageCompiler:s,fallbackLocale:a,messages:l}=e,[i,E]=it(...t),_=N(E.missingWarn)?E.missingWarn:e.missingWarn,T=N(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn,A=N(E.escapeParameter)?E.escapeParameter:e.escapeParameter,C=!!E.resolvedMessage,k=p(E.default)||N(E.default)?N(E.default)?s?i:()=>i:E.default:n?s?i:()=>i:null,h=n||null!=k&&(p(k)||d(k)),O=Te(e,E);A&&function(e){m(e.list)?e.list=e.list.map((e=>p(e)?f(e):e)):L(e.named)&&Object.keys(e.named).forEach((t=>{p(e.named[t])&&(e.named[t]=f(e.named[t]))}))}(E);let[I,g,S]=C?[i,O,l[O]||u()]:at(e,i,O,a,T,_),y=I,b=i;if(C||p(y)||Q(y)||st(y)||h&&(y=k,b=y),!(C||(p(y)||Q(y)||st(y))&&p(g)))return c?-1:i;let D=!1;const P=st(y)?y:lt(e,i,g,y,b,(()=>{D=!0}));if(D)return y;const R=function(e,t,n,r){const{modifiers:c,pluralRules:s,messageResolver:a,fallbackLocale:l,fallbackWarn:i,missingWarn:u,fallbackContext:f}=e,E=(r,o)=>{let c=a(n,r);if(null==c&&(f||o)){const[,,n]=at(f||e,r,t,l,i,u);c=a(n,r)}if(p(c)||Q(c)){let n=!1;const o=lt(e,r,t,c,r,(()=>{n=!0}));return n?ct:o}return st(c)?c:ct},_={locale:t,modifiers:c,pluralRules:s,messages:E};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);o(r.plural)&&(_.pluralIndex=r.plural);return _}(e,g,S,E),M=function(e,t,n){const r=t(n);return r}(0,P,ot(R));return r?r(M,i):M},e.translateDevTools=pe,e.updateFallbackLocale=function(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)},e}({});
