/**
* @vue/reactivity v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/let e,t,i,s,r;let n={},l=()=>{},o=Object.assign,a=(e,t)=>{let i=e.indexOf(t);i>-1&&e.splice(i,1)},u=Object.prototype.hasOwnProperty,h=(e,t)=>u.call(e,t),c=Array.isArray,f=e=>"[object Map]"===R(e),p=e=>"[object Set]"===R(e),d=e=>"function"==typeof e,_=e=>"string"==typeof e,v=e=>"symbol"==typeof e,g=e=>null!==e&&"object"==typeof e,y=Object.prototype.toString,R=e=>y.call(e),b=e=>R(e).slice(8,-1),w=e=>"[object Object]"===R(e),S=e=>_(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,E=(e,t)=>!Object.is(e,t),x=(e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})};class T{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=e,!t&&e&&(this.index=(e.scopes||(e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(t){if(this._active){let i=e;try{return e=this,t()}finally{e=i}}}on(){1==++this._on&&(this.prevScope=e,e=this)}off(){this._on>0&&0==--this._on&&(e=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,i;for(t=0,this._active=!1,i=this.effects.length;t<i;t++)this.effects[t].stop();for(t=0,this.effects.length=0,i=this.cleanups.length;t<i;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function m(e){return new T(e)}function A(){return e}function k(t,i=!1){e&&e.cleanups.push(t)}let D={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED",EVALUATED:128,128:"EVALUATED"},O=new WeakSet;class I{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,e&&e.active&&e.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,O.has(this)&&(O.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||j(this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,q(this),P(this);let e=t,i=H;t=this,H=!0;try{return this.fn()}finally{W(this),t=e,H=i,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)K(e);this.deps=this.depsTail=void 0,q(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?O.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){N(this)&&this.run()}get dirty(){return N(this)}}let L=0;function j(e,t=!1){if(e.flags|=8,t){e.next=s,s=e;return}e.next=i,i=e}function C(){let e;if(!(--L>0)){if(s){let e=s;for(s=void 0;e;){let t=e.next;e.next=void 0,e.flags&=-9,e=t}}for(;i;){let t=i;for(i=void 0;t;){let i=t.next;if(t.next=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=i}}if(e)throw e}}function P(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function W(e){let t,i=e.depsTail,s=i;for(;s;){let e=s.prevDep;-1===s.version?(s===i&&(i=e),K(s),function(e){let{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=e}e.deps=t,e.depsTail=i}function N(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(V(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function V(e){if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===J)||(e.globalVersion=J,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!N(e))))return;e.flags|=2;let i=e.dep,s=t,r=H;t=e,H=!0;try{P(e);let t=e.fn(e._value);(0===i.version||E(t,e._value))&&(e.flags|=128,e._value=t,i.version++)}catch(e){throw i.version++,e}finally{t=s,H=r,W(e),e.flags&=-3}}function K(e,t=!1){let{dep:i,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),i.subs===e&&(i.subs=s,!s&&i.computed)){i.computed.flags&=-5;for(let e=i.computed.deps;e;e=e.nextDep)K(e,!0)}t||--i.sc||!i.map||i.map.delete(i.key)}function M(e,t){e.effect instanceof I&&(e=e.effect.fn);let i=new I(e);t&&o(i,t);try{i.run()}catch(e){throw i.stop(),e}let s=i.run.bind(i);return s.effect=i,s}function U(e){e.effect.stop()}let H=!0,Y=[];function G(){Y.push(H),H=!1}function F(){Y.push(H),H=!0}function z(){let e=Y.pop();H=void 0===e||e}function B(e,i=!1){t instanceof I&&(t.cleanup=e)}function q(e){let{cleanup:i}=e;if(e.cleanup=void 0,i){let e=t;t=void 0;try{i()}finally{t=e}}}let J=0;class Q{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class X{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!t||!H||t===this.computed)return;let i=this.activeLink;if(void 0===i||i.sub!==t)i=this.activeLink=new Q(t,this),t.deps?(i.prevDep=t.depsTail,t.depsTail.nextDep=i,t.depsTail=i):t.deps=t.depsTail=i,function e(t){if(t.dep.sc++,4&t.sub.flags){let i=t.dep.computed;if(i&&!t.dep.subs){i.flags|=20;for(let t=i.deps;t;t=t.nextDep)e(t)}let s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}(i);else if(-1===i.version&&(i.version=this.version,i.nextDep)){let e=i.nextDep;e.prevDep=i.prevDep,i.prevDep&&(i.prevDep.nextDep=e),i.prevDep=t.depsTail,i.nextDep=void 0,t.depsTail.nextDep=i,t.depsTail=i,t.deps===i&&(t.deps=e)}return i}trigger(e){this.version++,J++,this.notify(e)}notify(e){L++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{C()}}}let Z=new WeakMap,$=Symbol(""),ee=Symbol(""),et=Symbol("");function ei(e,i,s){if(H&&t){let t=Z.get(e);t||Z.set(e,t=new Map);let i=t.get(s);i||(t.set(s,i=new X),i.map=t,i.key=s),i.track()}}function es(e,t,i,s,r,n){let l=Z.get(e);if(!l)return void J++;let o=e=>{e&&e.trigger()};if(L++,"clear"===t)l.forEach(o);else{let r=c(e),n=r&&S(i);if(r&&"length"===i){let e=Number(s);l.forEach((t,i)=>{("length"===i||i===et||!v(i)&&i>=e)&&o(t)})}else switch((void 0!==i||l.has(void 0))&&o(l.get(i)),n&&o(l.get(et)),t){case"add":r?n&&o(l.get("length")):(o(l.get($)),f(e)&&o(l.get(ee)));break;case"delete":!r&&(o(l.get($)),f(e)&&o(l.get(ee)));break;case"set":f(e)&&o(l.get($))}}C()}function er(e){let t=eG(e);return t===e?t:(ei(t,"iterate",et),eH(e)?t:t.map(ez))}function en(e){return ei(e=eG(e),"iterate",et),e}let el={__proto__:null,[Symbol.iterator](){return eo(this,Symbol.iterator,ez)},concat(...e){return er(this).concat(...e.map(e=>c(e)?er(e):e))},entries(){return eo(this,"entries",e=>(e[1]=ez(e[1]),e))},every(e,t){return eu(this,"every",e,t,void 0,arguments)},filter(e,t){return eu(this,"filter",e,t,e=>e.map(ez),arguments)},find(e,t){return eu(this,"find",e,t,ez,arguments)},findIndex(e,t){return eu(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return eu(this,"findLast",e,t,ez,arguments)},findLastIndex(e,t){return eu(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return eu(this,"forEach",e,t,void 0,arguments)},includes(...e){return ec(this,"includes",e)},indexOf(...e){return ec(this,"indexOf",e)},join(e){return er(this).join(e)},lastIndexOf(...e){return ec(this,"lastIndexOf",e)},map(e,t){return eu(this,"map",e,t,void 0,arguments)},pop(){return ef(this,"pop")},push(...e){return ef(this,"push",e)},reduce(e,...t){return eh(this,"reduce",e,t)},reduceRight(e,...t){return eh(this,"reduceRight",e,t)},shift(){return ef(this,"shift")},some(e,t){return eu(this,"some",e,t,void 0,arguments)},splice(...e){return ef(this,"splice",e)},toReversed(){return er(this).toReversed()},toSorted(e){return er(this).toSorted(e)},toSpliced(...e){return er(this).toSpliced(...e)},unshift(...e){return ef(this,"unshift",e)},values(){return eo(this,"values",ez)}};function eo(e,t,i){let s=en(e),r=s[t]();return s===e||eH(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=i(e.value)),e}),r}let ea=Array.prototype;function eu(e,t,i,s,r,n){let l=en(e),o=l!==e&&!eH(e),a=l[t];if(a!==ea[t]){let t=a.apply(e,n);return o?ez(t):t}let u=i;l!==e&&(o?u=function(t,s){return i.call(this,ez(t),s,e)}:i.length>2&&(u=function(t,s){return i.call(this,t,s,e)}));let h=a.call(l,u,s);return o&&r?r(h):h}function eh(e,t,i,s){let r=en(e),n=i;return r!==e&&(eH(e)?i.length>3&&(n=function(t,s,r){return i.call(this,t,s,r,e)}):n=function(t,s,r){return i.call(this,t,ez(s),r,e)}),r[t](n,...s)}function ec(e,t,i){let s=eG(e);ei(s,"iterate",et);let r=s[t](...i);return(-1===r||!1===r)&&eY(i[0])?(i[0]=eG(i[0]),s[t](...i)):r}function ef(e,t,i=[]){G(),L++;let s=eG(e)[t].apply(e,i);return C(),z(),s}let ep=function(e){let t=Object.create(null);for(let i of e.split(","))t[i]=1;return e=>e in t}("__proto__,__v_isRef,__isVue"),ed=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(v));function e_(e){v(e)||(e=String(e));let t=eG(this);return ei(t,"has",e),t.hasOwnProperty(e)}class ev{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,i){if("__v_skip"===t)return e.__v_skip;let s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return i===(s?r?eC:ej:r?eL:eI).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;let n=c(e);if(!s){let e;if(n&&(e=el[t]))return e;if("hasOwnProperty"===t)return e_}let l=Reflect.get(e,t,eq(e)?e:i);return(v(t)?ed.has(t):ep(t))||(s||ei(e,"get",t),r)?l:eq(l)?n&&S(t)?l:l.value:g(l)?s?eN(l):eP(l):l}}class eg extends ev{constructor(e=!1){super(!1,e)}set(e,t,i,s){let r=e[t];if(!this._isShallow){let t=eU(r);if(eH(i)||eU(i)||(r=eG(r),i=eG(i)),!c(e)&&eq(r)&&!eq(i))if(t)return!1;else return r.value=i,!0}let n=c(e)&&S(t)?Number(t)<e.length:h(e,t),l=Reflect.set(e,t,i,eq(e)?e:s);return e===eG(s)&&(n?E(i,r)&&es(e,"set",t,i):es(e,"add",t,i)),l}deleteProperty(e,t){let i=h(e,t);e[t];let s=Reflect.deleteProperty(e,t);return s&&i&&es(e,"delete",t,void 0),s}has(e,t){let i=Reflect.has(e,t);return v(t)&&ed.has(t)||ei(e,"has",t),i}ownKeys(e){return ei(e,"iterate",c(e)?"length":$),Reflect.ownKeys(e)}}class ey extends ev{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let eR=new eg,eb=new ey,ew=new eg(!0),eS=new ey(!0),eE=e=>e,ex=e=>Reflect.getPrototypeOf(e);function eT(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function em(e,t){let i=function(e,t){let i={get(i){let s=this.__v_raw,r=eG(s),n=eG(i);e||(E(i,n)&&ei(r,"get",i),ei(r,"get",n));let{has:l}=ex(r),o=t?eE:e?eB:ez;return l.call(r,i)?o(s.get(i)):l.call(r,n)?o(s.get(n)):void(s!==r&&s.get(i))},get size(){let t=this.__v_raw;return e||ei(eG(t),"iterate",$),Reflect.get(t,"size",t)},has(t){let i=this.__v_raw,s=eG(i),r=eG(t);return e||(E(t,r)&&ei(s,"has",t),ei(s,"has",r)),t===r?i.has(t):i.has(t)||i.has(r)},forEach(i,s){let r=this,n=r.__v_raw,l=eG(n),o=t?eE:e?eB:ez;return e||ei(l,"iterate",$),n.forEach((e,t)=>i.call(s,o(e),o(t),r))}};return o(i,e?{add:eT("add"),set:eT("set"),delete:eT("delete"),clear:eT("clear")}:{add(e){t||eH(e)||eU(e)||(e=eG(e));let i=eG(this);return ex(i).has.call(i,e)||(i.add(e),es(i,"add",e,e)),this},set(e,i){t||eH(i)||eU(i)||(i=eG(i));let s=eG(this),{has:r,get:n}=ex(s),l=r.call(s,e);l||(e=eG(e),l=r.call(s,e));let o=n.call(s,e);return s.set(e,i),l?E(i,o)&&es(s,"set",e,i):es(s,"add",e,i),this},delete(e){let t=eG(this),{has:i,get:s}=ex(t),r=i.call(t,e);r||(e=eG(e),r=i.call(t,e)),s&&s.call(t,e);let n=t.delete(e);return r&&es(t,"delete",e,void 0),n},clear(){let e=eG(this),t=0!==e.size,i=e.clear();return t&&es(e,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{i[s]=function(...i){let r=this.__v_raw,n=eG(r),l=f(n),o="entries"===s||s===Symbol.iterator&&l,a=r[s](...i),u=t?eE:e?eB:ez;return e||ei(n,"iterate","keys"===s&&l?ee:$),{next(){let{value:e,done:t}=a.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}),i}(e,t);return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(h(i,s)&&s in t?i:t,s,r)}let eA={get:em(!1,!1)},ek={get:em(!1,!0)},eD={get:em(!0,!1)},eO={get:em(!0,!0)},eI=new WeakMap,eL=new WeakMap,ej=new WeakMap,eC=new WeakMap;function eP(e){return eU(e)?e:eK(e,!1,eR,eA,eI)}function eW(e){return eK(e,!1,ew,ek,eL)}function eN(e){return eK(e,!0,eb,eD,ej)}function eV(e){return eK(e,!0,eS,eO,eC)}function eK(e,t,i,s,r){if(!g(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let n=e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(b(e));if(0===n)return e;let l=r.get(e);if(l)return l;let o=new Proxy(e,2===n?s:i);return r.set(e,o),o}function eM(e){return eU(e)?eM(e.__v_raw):!!(e&&e.__v_isReactive)}function eU(e){return!!(e&&e.__v_isReadonly)}function eH(e){return!!(e&&e.__v_isShallow)}function eY(e){return!!e&&!!e.__v_raw}function eG(e){let t=e&&e.__v_raw;return t?eG(t):e}function eF(e){return!h(e,"__v_skip")&&Object.isExtensible(e)&&x(e,"__v_skip",!0),e}let ez=e=>g(e)?eP(e):e,eB=e=>g(e)?eN(e):e;function eq(e){return!!e&&!0===e.__v_isRef}function eJ(e){return eX(e,!1)}function eQ(e){return eX(e,!0)}function eX(e,t){return eq(e)?e:new eZ(e,t)}class eZ{constructor(e,t){this.dep=new X,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:eG(e),this._value=t?e:ez(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,i=this.__v_isShallow||eH(e)||eU(e);E(e=i?e:eG(e),t)&&(this._rawValue=e,this._value=i?e:ez(e),this.dep.trigger())}}function e$(e){e.dep&&e.dep.trigger()}function e0(e){return eq(e)?e.value:e}function e1(e){return d(e)?e():e0(e)}let e2={get:(e,t,i)=>"__v_raw"===t?e:e0(Reflect.get(e,t,i)),set:(e,t,i,s)=>{let r=e[t];return eq(r)&&!eq(i)?(r.value=i,!0):Reflect.set(e,t,i,s)}};function e4(e){return eM(e)?e:new Proxy(e,e2)}class e6{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new X,{get:i,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=i,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}function e3(e){return new e6(e)}function e8(e){let t=c(e)?Array(e.length):{};for(let i in e)t[i]=te(e,i);return t}class e5{constructor(e,t,i){this._object=e,this._key=t,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){let i=Z.get(e);return i&&i.get(t)}(eG(this._object),this._key)}}class e9{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function e7(e,t,i){return eq(e)?e:d(e)?new e9(e):g(e)&&arguments.length>1?te(e,t,i):eJ(e)}function te(e,t,i){let s=e[t];return eq(s)?s:new e5(e,t,i)}class tt{constructor(e,t,i){this.fn=e,this.setter=t,this._value=void 0,this.dep=new X(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=J-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=i}notify(){if(this.flags|=16,!(8&this.flags)&&t!==this)return j(this,!0),!0}get value(){let e=this.dep.track();return V(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function ti(e,t,i=!1){let s,r;return d(e)?s=e:(s=e.get,r=e.set),new tt(s,r,i)}let ts={GET:"get",HAS:"has",ITERATE:"iterate"},tr={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},tn={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},tl={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},to={},ta=new WeakMap;function tu(){return r}function th(e,t=!1,i=r){if(i){let t=ta.get(i);t||ta.set(i,t=[]),t.push(e)}}function tc(e,t,i=n){let s,o,u,h,{immediate:f,deep:p,once:_,scheduler:v,augmentJob:g,call:y}=i,R=e=>p?e:eH(e)||!1===p||0===p?tf(e,1):tf(e),b=!1,w=!1;if(eq(e)?(o=()=>e.value,b=eH(e)):eM(e)?(o=()=>R(e),b=!0):c(e)?(w=!0,b=e.some(e=>eM(e)||eH(e)),o=()=>e.map(e=>eq(e)?e.value:eM(e)?R(e):d(e)?y?y(e,2):e():void 0)):o=d(e)?t?y?()=>y(e,2):e:()=>{if(u){G();try{u()}finally{z()}}let t=r;r=s;try{return y?y(e,3,[h]):e(h)}finally{r=t}}:l,t&&p){let e=o,t=!0===p?1/0:p;o=()=>tf(e(),t)}let S=A(),x=()=>{s.stop(),S&&S.active&&a(S.effects,s)};if(_&&t){let e=t;t=(...t)=>{e(...t),x()}}let T=w?Array(e.length).fill(to):to,m=e=>{if(1&s.flags&&(s.dirty||e))if(t){let e=s.run();if(p||b||(w?e.some((e,t)=>E(e,T[t])):E(e,T))){u&&u();let i=r;r=s;try{let i=[e,T===to?void 0:w&&T[0]===to?[]:T,h];T=e,y?y(t,3,i):t(...i)}finally{r=i}}}else s.run()};return g&&g(m),(s=new I(o)).scheduler=v?()=>v(m,!1):m,h=e=>th(e,!1,s),u=s.onStop=()=>{let e=ta.get(s);if(e){if(y)y(e,4);else for(let t of e)t();ta.delete(s)}},t?f?m(!0):T=s.run():v?v(m.bind(null,!0),!0):s.run(),x.pause=s.pause.bind(s),x.resume=s.resume.bind(s),x.stop=x,x}function tf(e,t=1/0,i){if(t<=0||!g(e)||e.__v_skip||(i=i||new Set).has(e))return e;if(i.add(e),t--,eq(e))tf(e.value,t,i);else if(c(e))for(let s=0;s<e.length;s++)tf(e[s],t,i);else if(p(e)||f(e))e.forEach(e=>{tf(e,t,i)});else if(w(e)){for(let s in e)tf(e[s],t,i);for(let s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&tf(e[s],t,i)}return e}export{et as ARRAY_ITERATE_KEY,D as EffectFlags,T as EffectScope,$ as ITERATE_KEY,ee as MAP_KEY_ITERATE_KEY,I as ReactiveEffect,tn as ReactiveFlags,ts as TrackOpTypes,tr as TriggerOpTypes,tl as WatchErrorCodes,ti as computed,e3 as customRef,M as effect,m as effectScope,F as enableTracking,A as getCurrentScope,tu as getCurrentWatcher,eY as isProxy,eM as isReactive,eU as isReadonly,eq as isRef,eH as isShallow,eF as markRaw,B as onEffectCleanup,k as onScopeDispose,th as onWatcherCleanup,G as pauseTracking,e4 as proxyRefs,eP as reactive,er as reactiveReadArray,eN as readonly,eJ as ref,z as resetTracking,eW as shallowReactive,en as shallowReadArray,eV as shallowReadonly,eQ as shallowRef,U as stop,eG as toRaw,ez as toReactive,eB as toReadonly,e7 as toRef,e8 as toRefs,e1 as toValue,ei as track,tf as traverse,es as trigger,e$ as triggerRef,e0 as unref,tc as watch};
