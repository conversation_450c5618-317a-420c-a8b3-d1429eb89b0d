/**
* @vue/reactivity v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/var VueReactivity=function(e){"use strict";let t,i,s,r,n,l={},o=()=>{},a=Object.assign,u=(e,t)=>{let i=e.indexOf(t);i>-1&&e.splice(i,1)},h=Object.prototype.hasOwnProperty,c=(e,t)=>h.call(e,t),f=Array.isArray,p=e=>"[object Map]"===b(e),d=e=>"[object Set]"===b(e),_=e=>"function"==typeof e,v=e=>"string"==typeof e,g=e=>"symbol"==typeof e,y=e=>null!==e&&"object"==typeof e,R=Object.prototype.toString,b=e=>R.call(e),w=e=>b(e).slice(8,-1),S=e=>"[object Object]"===b(e),E=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,x=(e,t)=>!Object.is(e,t),T=(e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})};class m{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=t,!e&&t&&(this.index=(t.scopes||(t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){let i=t;try{return t=this,e()}finally{t=i}}}on(){1==++this._on&&(this.prevScope=t,t=this)}off(){this._on>0&&0==--this._on&&(t=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,i;for(t=0,this._active=!1,i=this.effects.length;t<i;t++)this.effects[t].stop();for(t=0,this.effects.length=0,i=this.cleanups.length;t<i;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}let A=new WeakSet;class k{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,t&&t.active&&t.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,A.has(this)&&(A.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||O(this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,U(this),L(this);let e=i,t=N;i=this,N=!0;try{return this.fn()}finally{j(this),i=e,N=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)W(e);this.deps=this.depsTail=void 0,U(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?A.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){C(this)&&this.run()}get dirty(){return C(this)}}let D=0;function O(e,t=!1){if(e.flags|=8,t){e.next=r,r=e;return}e.next=s,s=e}function I(){let e;if(!(--D>0)){if(r){let e=r;for(r=void 0;e;){let t=e.next;e.next=void 0,e.flags&=-9,e=t}}for(;s;){let t=s;for(s=void 0;t;){let i=t.next;if(t.next=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=i}}if(e)throw e}}function L(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function j(e){let t,i=e.depsTail,s=i;for(;s;){let e=s.prevDep;-1===s.version?(s===i&&(i=e),W(s),function(e){let{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=e}e.deps=t,e.depsTail=i}function C(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(P(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function P(e){if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===H)||(e.globalVersion=H,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!C(e))))return;e.flags|=2;let t=e.dep,s=i,r=N;i=e,N=!0;try{L(e);let i=e.fn(e._value);(0===t.version||x(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(e){throw t.version++,e}finally{i=s,N=r,j(e),e.flags&=-3}}function W(e,t=!1){let{dep:i,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),i.subs===e&&(i.subs=s,!s&&i.computed)){i.computed.flags&=-5;for(let e=i.computed.deps;e;e=e.nextDep)W(e,!0)}t||--i.sc||!i.map||i.map.delete(i.key)}let N=!0,V=[];function K(){V.push(N),N=!1}function M(){let e=V.pop();N=void 0===e||e}function U(e){let{cleanup:t}=e;if(e.cleanup=void 0,t){let e=i;i=void 0;try{t()}finally{i=e}}}let H=0;class Y{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class G{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!i||!N||i===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==i)t=this.activeLink=new Y(i,this),i.deps?(t.prevDep=i.depsTail,i.depsTail.nextDep=t,i.depsTail=t):i.deps=i.depsTail=t,function e(t){if(t.dep.sc++,4&t.sub.flags){let i=t.dep.computed;if(i&&!t.dep.subs){i.flags|=20;for(let t=i.deps;t;t=t.nextDep)e(t)}let s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){let e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=i.depsTail,t.nextDep=void 0,i.depsTail.nextDep=t,i.depsTail=t,i.deps===t&&(i.deps=e)}return t}trigger(e){this.version++,H++,this.notify(e)}notify(e){D++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{I()}}}let F=new WeakMap,z=Symbol(""),B=Symbol(""),q=Symbol("");function J(e,t,s){if(N&&i){let t=F.get(e);t||F.set(e,t=new Map);let i=t.get(s);i||(t.set(s,i=new G),i.map=t,i.key=s),i.track()}}function Q(e,t,i,s,r,n){let l=F.get(e);if(!l)return void H++;let o=e=>{e&&e.trigger()};if(D++,"clear"===t)l.forEach(o);else{let r=f(e),n=r&&E(i);if(r&&"length"===i){let e=Number(s);l.forEach((t,i)=>{("length"===i||i===q||!g(i)&&i>=e)&&o(t)})}else switch((void 0!==i||l.has(void 0))&&o(l.get(i)),n&&o(l.get(q)),t){case"add":r?n&&o(l.get("length")):(o(l.get(z)),p(e)&&o(l.get(B)));break;case"delete":!r&&(o(l.get(z)),p(e)&&o(l.get(B)));break;case"set":p(e)&&o(l.get(z))}}I()}function X(e){let t=eP(e);return t===e?t:(J(t,"iterate",q),ej(e)?t:t.map(eW))}function Z(e){return J(e=eP(e),"iterate",q),e}let $={__proto__:null,[Symbol.iterator](){return ee(this,Symbol.iterator,eW)},concat(...e){return X(this).concat(...e.map(e=>f(e)?X(e):e))},entries(){return ee(this,"entries",e=>(e[1]=eW(e[1]),e))},every(e,t){return ei(this,"every",e,t,void 0,arguments)},filter(e,t){return ei(this,"filter",e,t,e=>e.map(eW),arguments)},find(e,t){return ei(this,"find",e,t,eW,arguments)},findIndex(e,t){return ei(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ei(this,"findLast",e,t,eW,arguments)},findLastIndex(e,t){return ei(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ei(this,"forEach",e,t,void 0,arguments)},includes(...e){return er(this,"includes",e)},indexOf(...e){return er(this,"indexOf",e)},join(e){return X(this).join(e)},lastIndexOf(...e){return er(this,"lastIndexOf",e)},map(e,t){return ei(this,"map",e,t,void 0,arguments)},pop(){return en(this,"pop")},push(...e){return en(this,"push",e)},reduce(e,...t){return es(this,"reduce",e,t)},reduceRight(e,...t){return es(this,"reduceRight",e,t)},shift(){return en(this,"shift")},some(e,t){return ei(this,"some",e,t,void 0,arguments)},splice(...e){return en(this,"splice",e)},toReversed(){return X(this).toReversed()},toSorted(e){return X(this).toSorted(e)},toSpliced(...e){return X(this).toSpliced(...e)},unshift(...e){return en(this,"unshift",e)},values(){return ee(this,"values",eW)}};function ee(e,t,i){let s=Z(e),r=s[t]();return s===e||ej(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=i(e.value)),e}),r}let et=Array.prototype;function ei(e,t,i,s,r,n){let l=Z(e),o=l!==e&&!ej(e),a=l[t];if(a!==et[t]){let t=a.apply(e,n);return o?eW(t):t}let u=i;l!==e&&(o?u=function(t,s){return i.call(this,eW(t),s,e)}:i.length>2&&(u=function(t,s){return i.call(this,t,s,e)}));let h=a.call(l,u,s);return o&&r?r(h):h}function es(e,t,i,s){let r=Z(e),n=i;return r!==e&&(ej(e)?i.length>3&&(n=function(t,s,r){return i.call(this,t,s,r,e)}):n=function(t,s,r){return i.call(this,t,eW(s),r,e)}),r[t](n,...s)}function er(e,t,i){let s=eP(e);J(s,"iterate",q);let r=s[t](...i);return(-1===r||!1===r)&&eC(i[0])?(i[0]=eP(i[0]),s[t](...i)):r}function en(e,t,i=[]){K(),D++;let s=eP(e)[t].apply(e,i);return I(),M(),s}let el=function(e){let t=Object.create(null);for(let i of e.split(","))t[i]=1;return e=>e in t}("__proto__,__v_isRef,__isVue"),eo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(g));function ea(e){g(e)||(e=String(e));let t=eP(this);return J(t,"has",e),t.hasOwnProperty(e)}class eu{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,i){if("__v_skip"===t)return e.__v_skip;let s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return i===(s?r?eA:em:r?eT:ex).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;let n=f(e);if(!s){let e;if(n&&(e=$[t]))return e;if("hasOwnProperty"===t)return ea}let l=Reflect.get(e,t,eV(e)?e:i);return(g(t)?eo.has(t):el(t))||(s||J(e,"get",t),r)?l:eV(l)?n&&E(t)?l:l.value:y(l)?s?eD(l):ek(l):l}}class eh extends eu{constructor(e=!1){super(!1,e)}set(e,t,i,s){let r=e[t];if(!this._isShallow){let t=eL(r);if(ej(i)||eL(i)||(r=eP(r),i=eP(i)),!f(e)&&eV(r)&&!eV(i))if(t)return!1;else return r.value=i,!0}let n=f(e)&&E(t)?Number(t)<e.length:c(e,t),l=Reflect.set(e,t,i,eV(e)?e:s);return e===eP(s)&&(n?x(i,r)&&Q(e,"set",t,i):Q(e,"add",t,i)),l}deleteProperty(e,t){let i=c(e,t);e[t];let s=Reflect.deleteProperty(e,t);return s&&i&&Q(e,"delete",t,void 0),s}has(e,t){let i=Reflect.has(e,t);return g(t)&&eo.has(t)||J(e,"has",t),i}ownKeys(e){return J(e,"iterate",f(e)?"length":z),Reflect.ownKeys(e)}}class ec extends eu{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let ef=new eh,ep=new ec,ed=new eh(!0),e_=new ec(!0),ev=e=>e,eg=e=>Reflect.getPrototypeOf(e);function ey(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function eR(e,t){let i=function(e,t){let i={get(i){let s=this.__v_raw,r=eP(s),n=eP(i);e||(x(i,n)&&J(r,"get",i),J(r,"get",n));let{has:l}=eg(r),o=t?ev:e?eN:eW;return l.call(r,i)?o(s.get(i)):l.call(r,n)?o(s.get(n)):void(s!==r&&s.get(i))},get size(){let t=this.__v_raw;return e||J(eP(t),"iterate",z),Reflect.get(t,"size",t)},has(t){let i=this.__v_raw,s=eP(i),r=eP(t);return e||(x(t,r)&&J(s,"has",t),J(s,"has",r)),t===r?i.has(t):i.has(t)||i.has(r)},forEach(i,s){let r=this,n=r.__v_raw,l=eP(n),o=t?ev:e?eN:eW;return e||J(l,"iterate",z),n.forEach((e,t)=>i.call(s,o(e),o(t),r))}};return a(i,e?{add:ey("add"),set:ey("set"),delete:ey("delete"),clear:ey("clear")}:{add(e){t||ej(e)||eL(e)||(e=eP(e));let i=eP(this);return eg(i).has.call(i,e)||(i.add(e),Q(i,"add",e,e)),this},set(e,i){t||ej(i)||eL(i)||(i=eP(i));let s=eP(this),{has:r,get:n}=eg(s),l=r.call(s,e);l||(e=eP(e),l=r.call(s,e));let o=n.call(s,e);return s.set(e,i),l?x(i,o)&&Q(s,"set",e,i):Q(s,"add",e,i),this},delete(e){let t=eP(this),{has:i,get:s}=eg(t),r=i.call(t,e);r||(e=eP(e),r=i.call(t,e)),s&&s.call(t,e);let n=t.delete(e);return r&&Q(t,"delete",e,void 0),n},clear(){let e=eP(this),t=0!==e.size,i=e.clear();return t&&Q(e,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{i[s]=function(...i){let r=this.__v_raw,n=eP(r),l=p(n),o="entries"===s||s===Symbol.iterator&&l,a=r[s](...i),u=t?ev:e?eN:eW;return e||J(n,"iterate","keys"===s&&l?B:z),{next(){let{value:e,done:t}=a.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}),i}(e,t);return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(c(i,s)&&s in t?i:t,s,r)}let eb={get:eR(!1,!1)},ew={get:eR(!1,!0)},eS={get:eR(!0,!1)},eE={get:eR(!0,!0)},ex=new WeakMap,eT=new WeakMap,em=new WeakMap,eA=new WeakMap;function ek(e){return eL(e)?e:eO(e,!1,ef,eb,ex)}function eD(e){return eO(e,!0,ep,eS,em)}function eO(e,t,i,s,r){if(!y(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let n=e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(w(e));if(0===n)return e;let l=r.get(e);if(l)return l;let o=new Proxy(e,2===n?s:i);return r.set(e,o),o}function eI(e){return eL(e)?eI(e.__v_raw):!!(e&&e.__v_isReactive)}function eL(e){return!!(e&&e.__v_isReadonly)}function ej(e){return!!(e&&e.__v_isShallow)}function eC(e){return!!e&&!!e.__v_raw}function eP(e){let t=e&&e.__v_raw;return t?eP(t):e}let eW=e=>y(e)?ek(e):e,eN=e=>y(e)?eD(e):e;function eV(e){return!!e&&!0===e.__v_isRef}function eK(e){return eM(e,!1)}function eM(e,t){return eV(e)?e:new eU(e,t)}class eU{constructor(e,t){this.dep=new G,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:eP(e),this._value=t?e:eW(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,i=this.__v_isShallow||ej(e)||eL(e);x(e=i?e:eP(e),t)&&(this._rawValue=e,this._value=i?e:eW(e),this.dep.trigger())}}function eH(e){return eV(e)?e.value:e}let eY={get:(e,t,i)=>"__v_raw"===t?e:eH(Reflect.get(e,t,i)),set:(e,t,i,s)=>{let r=e[t];return eV(r)&&!eV(i)?(r.value=i,!0):Reflect.set(e,t,i,s)}};class eG{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new G,{get:i,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=i,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}class eF{constructor(e,t,i){this._object=e,this._key=t,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){let i=F.get(e);return i&&i.get(t)}(eP(this._object),this._key)}}class ez{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function eB(e,t,i){let s=e[t];return eV(s)?s:new eF(e,t,i)}class eq{constructor(e,t,i){this.fn=e,this.setter=t,this._value=void 0,this.dep=new G(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=H-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=i}notify(){if(this.flags|=16,!(8&this.flags)&&i!==this)return O(this,!0),!0}get value(){let e=this.dep.track();return P(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}let eJ={},eQ=new WeakMap;function eX(e,t=!1,i=n){if(i){let t=eQ.get(i);t||eQ.set(i,t=[]),t.push(e)}}function eZ(e,t=1/0,i){if(t<=0||!y(e)||e.__v_skip||(i=i||new Set).has(e))return e;if(i.add(e),t--,eV(e))eZ(e.value,t,i);else if(f(e))for(let s=0;s<e.length;s++)eZ(e[s],t,i);else if(d(e)||p(e))e.forEach(e=>{eZ(e,t,i)});else if(S(e)){for(let s in e)eZ(e[s],t,i);for(let s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&eZ(e[s],t,i)}return e}return e.ARRAY_ITERATE_KEY=q,e.EffectFlags={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED",EVALUATED:128,128:"EVALUATED"},e.EffectScope=m,e.ITERATE_KEY=z,e.MAP_KEY_ITERATE_KEY=B,e.ReactiveEffect=k,e.ReactiveFlags={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},e.TrackOpTypes={GET:"get",HAS:"has",ITERATE:"iterate"},e.TriggerOpTypes={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},e.WatchErrorCodes={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},e.computed=function(e,t,i=!1){let s,r;return _(e)?s=e:(s=e.get,r=e.set),new eq(s,r,i)},e.customRef=function(e){return new eG(e)},e.effect=function(e,t){e.effect instanceof k&&(e=e.effect.fn);let i=new k(e);t&&a(i,t);try{i.run()}catch(e){throw i.stop(),e}let s=i.run.bind(i);return s.effect=i,s},e.effectScope=function(e){return new m(e)},e.enableTracking=function(){V.push(N),N=!0},e.getCurrentScope=function(){return t},e.getCurrentWatcher=function(){return n},e.isProxy=eC,e.isReactive=eI,e.isReadonly=eL,e.isRef=eV,e.isShallow=ej,e.markRaw=function(e){return!c(e,"__v_skip")&&Object.isExtensible(e)&&T(e,"__v_skip",!0),e},e.onEffectCleanup=function(e,t=!1){i instanceof k&&(i.cleanup=e)},e.onScopeDispose=function(e,i=!1){t&&t.cleanups.push(e)},e.onWatcherCleanup=eX,e.pauseTracking=K,e.proxyRefs=function(e){return eI(e)?e:new Proxy(e,eY)},e.reactive=ek,e.reactiveReadArray=X,e.readonly=eD,e.ref=eK,e.resetTracking=M,e.shallowReactive=function(e){return eO(e,!1,ed,ew,eT)},e.shallowReadArray=Z,e.shallowReadonly=function(e){return eO(e,!0,e_,eE,eA)},e.shallowRef=function(e){return eM(e,!0)},e.stop=function(e){e.effect.stop()},e.toRaw=eP,e.toReactive=eW,e.toReadonly=eN,e.toRef=function(e,t,i){return eV(e)?e:_(e)?new ez(e):y(e)&&arguments.length>1?eB(e,t,i):eK(e)},e.toRefs=function(e){let t=f(e)?Array(e.length):{};for(let i in e)t[i]=eB(e,i);return t},e.toValue=function(e){return _(e)?e():eH(e)},e.track=J,e.traverse=eZ,e.trigger=Q,e.triggerRef=function(e){e.dep&&e.dep.trigger()},e.unref=eH,e.watch=function(e,i,s=l){let r,a,h,c,{immediate:p,deep:d,once:v,scheduler:g,augmentJob:y,call:R}=s,b=e=>d?e:ej(e)||!1===d||0===d?eZ(e,1):eZ(e),w=!1,S=!1;if(eV(e)?(a=()=>e.value,w=ej(e)):eI(e)?(a=()=>b(e),w=!0):f(e)?(S=!0,w=e.some(e=>eI(e)||ej(e)),a=()=>e.map(e=>eV(e)?e.value:eI(e)?b(e):_(e)?R?R(e,2):e():void 0)):a=_(e)?i?R?()=>R(e,2):e:()=>{if(h){K();try{h()}finally{M()}}let t=n;n=r;try{return R?R(e,3,[c]):e(c)}finally{n=t}}:o,i&&d){let e=a,t=!0===d?1/0:d;a=()=>eZ(e(),t)}let E=t,T=()=>{r.stop(),E&&E.active&&u(E.effects,r)};if(v&&i){let e=i;i=(...t)=>{e(...t),T()}}let m=S?Array(e.length).fill(eJ):eJ,A=e=>{if(1&r.flags&&(r.dirty||e))if(i){let e=r.run();if(d||w||(S?e.some((e,t)=>x(e,m[t])):x(e,m))){h&&h();let t=n;n=r;try{let t=[e,m===eJ?void 0:S&&m[0]===eJ?[]:m,c];m=e,R?R(i,3,t):i(...t)}finally{n=t}}}else r.run()};return y&&y(A),(r=new k(a)).scheduler=g?()=>g(A,!1):A,c=e=>eX(e,!1,r),h=r.onStop=()=>{let e=eQ.get(r);if(e){if(R)R(e,4);else for(let t of e)t();eQ.delete(r)}},i?p?A(!0):m=r.run():g?g(A.bind(null,!0),!0):r.run(),T.pause=r.pause.bind(r),T.resume=r.resume.bind(r),T.stop=T,T},e}({});
