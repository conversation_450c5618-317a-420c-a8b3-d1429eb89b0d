<template>
  <div class="overflow-y-auto">
    <div class="relative grow overflow-y-auto px-6 pb-[50px]">
      <ConfigPrompt :promptTemplate="modelConfig.configs.prompt_template" @updatePrompt="updatePrompt" />

      <ConfigVar
        :promptVariables="modelConfig.configs.prompt_variables"
        @onPromptVariablesChange="onPromptVariablesChange"
      />

      <DatasetConfig :dataSets="modelConfig.dataSets" :datasetConfigs="datasetConfigs" @addDataset="addDataset" />
      <SelectDataset v-if="isDatasetModalVisible" @closeDatasetDialog="closeDatasetDialog" />

      <AgentTools :collectionList="collectionList" :selectedTools="modelConfig.agentConfig.tools" />
    </div>
  </div>
</template>
<script setup lang="ts">
import ConfigPrompt from '@/views/app/configuration/config-prompt/index.vue'
import ConfigVar from '@/views/app/configuration/config-var/index.vue'
import DatasetConfig from '@/views/app/configuration/dataset-config/DatasetConfig.vue'
import SelectDataset from '@/views/app/configuration/dataset-config/select-dataset/SelectDataset.vue'
import AgentTools from '@/views/app/configuration/agent-tools/AgentTools.vue'

const props = withDefaults(
  defineProps<{
    modelConfig?: any
    datasetConfigs?: any
    promptVariables?: any[]
    collectionList?: any[]
  }>(),
  {
    modelConfig: () => ({}) as any,
    datasetConfigs: () => ({}) as any,
    promptVariables: () => [],
    collectionList: () => [],
  }
)
const emits = defineEmits(['setModelConfig'])
const updatePrompt = (val: any) => {
  const modelConfig = {
    ...props.modelConfig
  }
  modelConfig.configs.prompt_template = val
  emits('setModelConfig', modelConfig)
}
/**
 * 更新变量
 * @param newVariables 变量的最新值
 */
const onPromptVariablesChange = (newVariables: any[]) => {
  console.log('formList更新：', newVariables)
  const modelConfig = {
    ...props.modelConfig
  }
  modelConfig.configs.prompt_variables = newVariables
  // emits('onPromptVariablesChange', newVariables)
  emits('setModelConfig', modelConfig)
}

const isDatasetModalVisible = ref(false)
const addDataset = (type) => {
  isDatasetModalVisible.value = true
}
const closeDatasetDialog = (type: string) => {
  isDatasetModalVisible.value = false
}
</script>
<style scoped lang="scss"></style>
