hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@intlify/core-base@11.1.5':
    '@intlify/core-base': private
  '@intlify/message-compiler@11.1.5':
    '@intlify/message-compiler': private
  '@intlify/shared@11.1.5':
    '@intlify/shared': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.16(vue@3.5.16)':
    '@vue/server-renderer': private
  '@vue/shared@3.5.16':
    '@vue/shared': private
  csstype@3.1.3:
    csstype: private
  entities@4.5.0:
    entities: private
  estree-walker@2.0.2:
    estree-walker: private
  magic-string@0.30.17:
    magic-string: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  postcss@8.5.5:
    postcss: private
  source-map-js@1.2.1:
    source-map-js: private
  vue@3.5.16:
    vue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 13 Jun 2025 03:10:06 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: F:\.pnpm-store\v10
virtualStoreDir: F:\ai大模型平台\node_modules\.pnpm
virtualStoreDirMaxLength: 60
