<template>
  <div class="workflow-history-container">
    <!-- 触发按钮 -->
    <div
      :class="[
        'history-trigger',
        {
          'is-active': open,
          'is-disabled': nodesReadOnly
        }
      ]"
      @click="handleToggle"
    >
      <icon-history class="trigger-icon" />
    </div>

    <!-- 历史面板 -->
    <div v-if="open" class="history-panel" @click.stop>
      <!-- 头部 -->
      <div class="panel-header">
        <div class="header-left">
          <icon-history class="header-icon" />
          <span class="header-title">{{ t('workflow.changeHistory.title') }}</span>
        </div>
        <div class="header-right">
          <button
            v-if="hasHistory"
            class="clear-button"
            @click="handleClearHistory"
          >
            {{ t('workflow.changeHistory.clear') }}
          </button>
          <button class="close-button" @click="handleClose">
            <icon-close />
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="panel-content">
        <!-- 未来状态 (可重做) -->
        <div v-if="futureStates.length > 0" class="history-section">
          <div
            v-for="(item, index) in futureStates"
            :key="`future-${index}`"
            :class="[
              'history-item',
              {
                'is-current': item.isCurrent,
                'is-future': true
              }
            ]"
            @click="() => handleJumpToState(item)"
          >
            <div class="item-content">
              <div class="item-label">
                {{ getHistoryLabel(item.event) }}
                <span class="step-info">
                  ({{ calculateStepLabel(item.stepCount) }}{{ item.isCurrent ? t('workflow.changeHistory.currentState') : '' }})
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 当前状态 -->
        <div class="history-section">
          <div class="history-item is-current">
            <div class="item-content">
              <div class="item-label">
                {{ t('workflow.changeHistory.currentState') }}
              </div>
            </div>
          </div>
        </div>

        <!-- 过去状态 (可撤销) -->
        <div v-if="pastStates.length > 0" class="history-section">
          <div
            v-for="(item, index) in pastStates.slice().reverse()"
            :key="`past-${index}`"
            :class="[
              'history-item',
              {
                'is-current': item.isCurrent,
                'is-past': true
              }
            ]"
            @click="() => handleJumpToState(item)"
          >
            <div class="item-content">
              <div class="item-label">
                {{ getHistoryLabel(item.event) }}
                <span class="step-info">
                  ({{ calculateStepLabel(item.stepCount) }}{{ item.isCurrent ? t('workflow.changeHistory.currentState') : '' }})
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!hasHistory" class="empty-state">
          <icon-history class="empty-icon" />
          <div class="empty-text">{{ t('workflow.changeHistory.empty') }}</div>
          <div class="empty-desc">{{ t('workflow.changeHistory.emptyDesc') }}</div>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div v-if="open" class="history-overlay" @click="handleClose"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

// 工作流历史事件类型 - 与 dify 保持一致
enum WorkflowHistoryEvent {
  NodeTitleChange = 'NodeTitleChange',
  NodeDescriptionChange = 'NodeDescriptionChange',
  NodeDragStop = 'NodeDragStop',
  NodeChange = 'NodeChange',
  NodeConnect = 'NodeConnect',
  NodePaste = 'NodePaste',
  NodeDelete = 'NodeDelete',
  EdgeDelete = 'EdgeDelete',
  EdgeDeleteByDeleteBranch = 'EdgeDeleteByDeleteBranch',
  NodeAdd = 'NodeAdd',
  NodeResize = 'NodeResize',
  NoteAdd = 'NoteAdd',
  NoteChange = 'NoteChange',
  NoteDelete = 'NoteDelete',
  LayoutOrganize = 'LayoutOrganize'
}

interface HistoryState {
  event: WorkflowHistoryEvent
  timestamp: number
  stepCount: number
  nodes: any[]
  edges: any[]
  isCurrent?: boolean
}

interface ChangeHistoryEntry {
  index: number
  label?: string
  event: WorkflowHistoryEvent
  stepCount: number
  isCurrent?: boolean
}

interface Props {
  pastStates: HistoryState[]
  futureStates: HistoryState[]
  currentHistoryIndex: number
  nodesReadOnly?: boolean
}

interface Emits {
  (e: 'jumpToState', state: HistoryState): void
  (e: 'clearHistory'): void
}

const props = withDefaults(defineProps<Props>(), {
  pastStates: () => [],
  futureStates: () => [],
  currentHistoryIndex: 0,
  nodesReadOnly: false
})

const emit = defineEmits<Emits>()
const { t } = useI18n()

// 响应式数据
const open = ref(false)

// 计算属性
const hasHistory = computed(() => {
  return props.pastStates.length > 0 || props.futureStates.length > 0
})

// 获取历史事件标签 - 与 dify 保持一致
const getHistoryLabel = (event: WorkflowHistoryEvent): string => {
  const labels = {
    [WorkflowHistoryEvent.NodeAdd]: t('workflow.changeHistory.nodeAdd'),
    [WorkflowHistoryEvent.NodeDelete]: t('workflow.changeHistory.nodeDelete'),
    [WorkflowHistoryEvent.NodeChange]: t('workflow.changeHistory.nodeChange'),
    [WorkflowHistoryEvent.NodeDragStop]: t('workflow.changeHistory.nodeMove'),
    [WorkflowHistoryEvent.NodeConnect]: t('workflow.changeHistory.nodeConnect'),
    [WorkflowHistoryEvent.EdgeDelete]: t('workflow.changeHistory.edgeDelete'),
    [WorkflowHistoryEvent.NoteAdd]: t('workflow.changeHistory.noteAdd'),
    [WorkflowHistoryEvent.NoteChange]: t('workflow.changeHistory.noteChange'),
    [WorkflowHistoryEvent.NoteDelete]: t('workflow.changeHistory.noteDelete'),
    [WorkflowHistoryEvent.LayoutOrganize]: t('workflow.changeHistory.layoutOrganize')
  }
  return labels[event] || t('workflow.changeHistory.unknown')
}

// 计算步数标签 - 与 dify 保持一致
const calculateStepLabel = (stepCount: number): string => {
  if (!stepCount) return ''
  
  const count = stepCount < 0 ? stepCount * -1 : stepCount
  return stepCount > 0 
    ? t('workflow.changeHistory.stepForward', { count })
    : t('workflow.changeHistory.stepBackward', { count })
}

// 方法
const handleToggle = () => {
  if (props.nodesReadOnly) return
  open.value = !open.value
}

const handleClose = () => {
  open.value = false
}

const handleClearHistory = () => {
  emit('clearHistory')
  handleClose()
}

const handleJumpToState = (state: HistoryState) => {
  emit('jumpToState', state)
  handleClose()
}
</script>

<style lang="scss" scoped>
.workflow-history-container {
  position: relative;
}

// 触发按钮样式 - 参考 dify
.history-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text-3);

  &:hover {
    background-color: var(--color-fill-2);
    color: var(--color-text-2);
  }

  &.is-active {
    background-color: var(--color-primary-light-1);
    color: var(--color-primary-6);
  }

  &.is-disabled {
    cursor: not-allowed;
    color: var(--color-text-4);
    
    &:hover {
      background-color: transparent;
      color: var(--color-text-4);
    }
  }

  .trigger-icon {
    width: 16px;
    height: 16px;
  }
}

// 历史面板样式 - 参考 dify
.history-panel {
  position: absolute;
  top: 40px;
  right: 0;
  width: 320px;
  max-height: 400px;
  background: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-2);
  background: var(--color-bg-1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .header-icon {
      width: 16px;
      height: 16px;
      color: var(--color-text-2);
    }

    .header-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-1);
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .clear-button {
      padding: 4px 8px;
      font-size: 12px;
      color: var(--color-text-3);
      background: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--color-fill-2);
        color: var(--color-text-2);
      }
    }

    .close-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      color: var(--color-text-3);
      transition: all 0.2s ease;

      &:hover {
        background: var(--color-fill-2);
        color: var(--color-text-2);
      }
    }
  }
}

.panel-content {
  max-height: 320px;
  overflow-y: auto;
  padding: 8px 0;
}

.history-section {
  .history-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 2px 8px;
    border-radius: 6px;

    &:hover {
      background: var(--color-fill-1);
    }

    &.is-current {
      background: var(--color-fill-2);
      font-weight: 500;
    }

    &.is-future {
      .item-label {
        color: var(--color-text-2);
      }
    }

    &.is-past {
      .item-label {
        color: var(--color-text-2);
      }
    }

    .item-content {
      flex: 1;

      .item-label {
        font-size: 13px;
        line-height: 18px;
        color: var(--color-text-1);

        .step-info {
          color: var(--color-text-3);
          font-weight: normal;
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .empty-icon {
    width: 32px;
    height: 32px;
    color: var(--color-text-4);
    margin-bottom: 12px;
  }

  .empty-text {
    font-size: 14px;
    color: var(--color-text-2);
    margin-bottom: 4px;
  }

  .empty-desc {
    font-size: 12px;
    color: var(--color-text-3);
  }
}

// 遮罩层
.history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}
</style>
