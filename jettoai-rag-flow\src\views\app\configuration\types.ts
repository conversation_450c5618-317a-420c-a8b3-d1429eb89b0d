export enum RerankingModeEnum {
  RerankingModel = 'reranking_model',
  WeightedScore = 'weighted_score',
}

export enum WeightedScoreEnum {
  SemanticFirst = 'semantic_first',
  KeywordFirst = 'keyword_first',
  Customized = 'customized',
}

// export type DataSet = {
//   id: string
//   name: string
//   icon: string
//   icon_background: string
//   description: string
//   permission: DatasetPermission
//   data_source_type: DataSourceType
//   indexing_technique: IndexingType
//   created_by: string
//   updated_by: string
//   updated_at: number
//   app_count: number
//   doc_form: ChunkingMode
//   document_count: number
//   word_count: number
//   provider: string
//   embedding_model: string
//   embedding_model_provider: string
//   embedding_available: boolean
//   retrieval_model_dict: RetrievalConfig
//   retrieval_model: RetrievalConfig
//   tags: Tag[]
//   partial_member_list?: string[]
//   external_knowledge_info: {
//     external_knowledge_id: string
//     external_knowledge_api_id: string
//     external_knowledge_api_name: string
//     external_knowledge_api_endpoint: string
//   }
//   external_retrieval_model: {
//     top_k: number
//     score_threshold: number
//     score_threshold_enabled: boolean
//   }
//   built_in_field_enabled: boolean
//   doc_metadata?: MetadataInDoc[]
// }
export type MultipleRetrievalConfig = {
  top_k: number
  score_threshold: number | null | undefined
  reranking_model?: {
    provider: string
    model: string
  }
  reranking_mode?: RerankingModeEnum
  weights?: {
    weight_type: WeightedScoreEnum
    vector_setting: {
      vector_weight: number
      embedding_provider_name: string
      embedding_model_name: string
    }
    keyword_setting: {
      keyword_weight: number
    }
  }
  reranking_enable?: boolean
}
