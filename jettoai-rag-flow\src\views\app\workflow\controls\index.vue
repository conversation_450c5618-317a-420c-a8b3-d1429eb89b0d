<template #controls>
  <Controls position="bottom-left" class="workflow-controls">
    <!-- 撤销 -->
    <ControlButton @click="undo">
      <a-tooltip content="撤销 (Ctrl+Z)">
        <a-button type="text" :disabled="!canUndo" class="control-btn">
          <template #icon><icon-undo /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <!-- 重做 -->
    <ControlButton @click="redo">
      <a-tooltip content="重做 (Ctrl+Y)">
        <a-button type="text" :disabled="!canRedo" class="control-btn">
          <template #icon><icon-redo /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <!-- 变更历史 -->
    <ControlButton @click="history">
      <a-tooltip content="变更历史">
        <a-button type="text" class="control-btn">
          <template #icon><icon-history /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <!-- 分隔线 -->
    <div class="control-divider" />

    <!-- 添加节点 -->
    <a-dropdown :popup-max-height="400" :trigger="['click']" position="top">
      <ControlButton>
        <a-tooltip content="添加节点">
          <a-button type="text" class="control-btn">
            <template #icon><icon-plus-circle /></template>
          </a-button>
        </a-tooltip>
      </ControlButton>
      <template #content>
        <div class="node-list" :style="{ width: divWidth }">
          <a-tabs v-model="tabvalue" @tab-click="tabClick">
            <a-tab-pane key="1" title="节点">
              <div class="node-grid">
                <a-tooltip v-for="item in nodeList" :key="item.type" position="right" :content="item.title">
                  <div class="node-list-item" @click="() => selectTool(item)">
                    <div class="node-list-item-icon" :style="{ backgroundColor: nodeColor[item.type] }">
                      <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${item.type}`" />
                    </div>
                    <div class="node-list-item-text">
                      {{ item.title }}
                    </div>
                  </div>
                </a-tooltip>
              </div>
            </a-tab-pane>
            <a-tab-pane key="2" title="工具">
              <div class="tool-grid">
                <div v-for="tool in toolList" :key="tool.id" class="tool-item" @click="() => selectTool(tool)">
                  <div class="tool-icon">
                    <AiSvgIcon :name="tool.icon" />
                  </div>
                  <div class="tool-name">{{ tool.name }}</div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </a-dropdown>

    <!-- 添加注释 -->
    <ControlButton @click="addNote">
      <a-tooltip content="添加注释">
        <a-button type="text" class="control-btn">
          <template #icon><icon-message /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <!-- 分隔线 -->
    <div class="control-divider" />

    <!-- 导出图片 -->
    <a-dropdown :trigger="['click']" position="top">
      <ControlButton>
        <a-tooltip content="导出图片">
          <a-button type="text" class="control-btn">
            <template #icon><icon-export /></template>
          </a-button>
        </a-tooltip>
      </ControlButton>
      <template #content>
        <div class="export-menu">
          <div class="export-item" @click="exportToImage('png')">
            <icon-image class="export-icon" />
            <span>导出为 PNG</span>
          </div>
          <div class="export-item" @click="exportToImage('jpg')">
            <icon-image class="export-icon" />
            <span>导出为 JPG</span>
          </div>
          <div class="export-item" @click="exportToImage('svg')">
            <icon-image class="export-icon" />
            <span>导出为 SVG</span>
          </div>
        </div>
      </template>
    </a-dropdown>

    <!-- 整理节点 -->
    <ControlButton @click="layoutGraph">
      <a-tooltip content="自动整理节点">
        <a-button type="text" class="control-btn">
          <template #icon><icon-sort /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <!-- 分隔线 -->
    <div class="control-divider" />

    <!-- 缩放控制 -->
    <ControlButton @click="handleZoomIn">
      <a-tooltip content="放大">
        <a-button type="text" class="control-btn">
          <template #icon><icon-plus /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <ControlButton @click="handleZoomOut">
      <a-tooltip content="缩小">
        <a-button type="text" class="control-btn">
          <template #icon><icon-minus /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>

    <ControlButton @click="handleFitView">
      <a-tooltip content="适应画布">
        <a-button type="text" class="control-btn">
          <template #icon><icon-fullscreen /></template>
        </a-button>
      </a-tooltip>
    </ControlButton>
  </Controls>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useVueFlow } from '@vue-flow/core'
import { Controls, ControlButton } from '@vue-flow/controls'
import '@vue-flow/controls/dist/style.css'
import { nanoid } from 'nanoid'
import { Message } from '@arco-design/web-vue'
import { toPng, toJpeg, toSvg } from 'html-to-image'

// 导入本地模块
import nodeUtils from '../utils/node-utils'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import { nodeColor } from '../types/workflow'
// 定义节点类型枚举
enum BlockEnum {
  LLM = 'llm',
  ParameterExtractor = 'parameter-extractor',
  HttpRequest = 'http-request',
  Code = 'code',
  Iteration = 'iteration',
  KnowledgeRetrieval = 'knowledge-retrieval',
  Answer = 'answer',
  End = 'end',
  IfElse = 'if-else',
  DocumentExtractor = 'document-extractor',
  Assigner = 'assigner',
  VariableAggregator = 'variable-aggregator',
  Tool = 'tool'
}

// 导入节点默认配置
import nodeDefault from '../nodes/llm/default'
import nodeDefaultHttp from '../nodes/http/default'
import nodeDefaultCode from '../nodes/code/default'
import nodeDefaultInteration from '../nodes/iteration/default'
import nodeDefaultParameter from '../nodes/parameter-extractor/default'

// API 导入 - 暂时注释掉不存在的API
// import { getModelDefaultHttp } from '@/apis/model-mgmt'

// 响应式数据
const tabvalue = ref('1')
const divWidth = ref('150px')
const canUndo = ref(false)
const canRedo = ref(false)
const toolList = ref([
  { id: 'tool1', name: '文本处理', icon: 'tool-text' },
  { id: 'tool2', name: '图像处理', icon: 'tool-image' },
  { id: 'tool3', name: '数据转换', icon: 'tool-data' },
  { id: 'tool4', name: 'API调用', icon: 'tool-api' }
])

// Vue Flow 实例
const { addNodes, getNodes, getEdges, zoomIn, zoomOut, fitView, getViewport, setViewport } = useVueFlow()
const nodesStore = useNodesStore()
const { addNodeToflow } = nodeUtils()

// 根据app模式过滤节点列表
const nodeList = computed(() => {
  // 暂时设置为默认模式，支持多种模式
  const mode: 'workflow' | 'advanced-chat' | 'chat' = 'workflow' // appInfo.value?.mode
  const allNodeList = [
    {
      title: 'LLM',
      type: 'llm',
      desc: '',
      ...nodeDefault.defaultValue
    },
    {
      title: '知识检索',
      type: 'knowledge-retrieval',
      desc: '',
      query_variable_selector: [],
      dataset_ids: [],
      retrieval_mode: 'single'
    },
    {
      title: '结束',
      type: 'end',
      desc: '',
      outputs: []
    },
    {
      title: '直接回复',
      type: 'answer',
      desc: '',
      answer: '',
      variables: []
    },
    {
      title: '迭代',
      type: 'iteration',
      desc: '',
      ...nodeDefaultInteration.defaultValue
    },
    {
      title: '条件分支',
      type: 'if-else',
      cases: [
        {
          id: 'true',
          case_id: 'true',
          logical_operator: 'and',
          conditions: []
        }
      ]
    },
    {
      title: '代码执行',
      type: 'code',
      desc: '',
      ...nodeDefaultCode.defaultValue
    },
    {
      title: '文档提取',
      type: 'document-extractor',
      desc: ''
    },
    {
      title: '变量赋值',
      type: 'assigner',
      desc: ''
    },
    {
      title: '变量聚合',
      type: 'variable-aggregator',
      desc: '',
      variables: [],
      output_type: 'any'
    },
    {
      title: '参数提取',
      type: 'parameter-extractor',
      desc: '',
      ...nodeDefaultParameter.defaultValue
    },
    {
      title: 'http请求',
      type: 'http-request',
      desc: '',
      ...nodeDefaultHttp.defaultValue
    }
  ]

  return allNodeList.filter((node) => {
    // 根据mode字段判断节点显示
    if (mode === 'advanced-chat') {
      // advanced-chat模式：不显示结束节点，显示answer节点
      return node.type !== 'end'
    } else if (mode === 'workflow') {
      // workflow模式：显示结束节点，不显示answer节点
      return node.type !== 'answer'
    } else {
      return true
    }
  })
})

// 标签页切换处理
const tabClick = (value: string) => {
  if (value === '2') {
    divWidth.value = '300px'
  } else {
    divWidth.value = '150px'
  }
}
const selectTool = async (toolItem: any) => {
  try {
    // 暂时注释掉API调用
    // if (toolItem.type === BlockEnum.LLM || toolItem.type === BlockEnum.ParameterExtractor) {
    //   // LLM的场景下：需要查询默认的llm，然后赋值。
    //   const { data: res } = await getModelDefaultHttp('llm')
    //   toolItem.model.provider = res.provider.provider
    //   toolItem.model.name = res.model
    // }

    const { newNodeProps } = await addNodeToflow({}, toolItem, '0')
    console.log(newNodeProps)

    // 如果是迭代类型，动态添加node
    nextTick(() => {
      if (newNodeProps.type === 'iteration') {
        addNodes({
          id: `node_${nanoid()}`,
          type: 'iteration-start',
          position: { x: 40, y: 80 },
          data: { title: '', desc: '', type: 'iteration-start', isInIteration: true },
          parentNode: newNodeProps.id,
          extent: 'parent',
          expandParent: true
        })
      }
    })
    nodesStore.setNodes(getNodes.value)
    nodesStore.setEdges(getEdges.value)

    Message.success(`已添加${toolItem.title}节点`)
  } catch (error) {
    console.error('添加节点失败:', error)
    Message.error('添加节点失败')
  }
}
// 事件定义
const emit = defineEmits<{
  (e: 'undo'): void
  (e: 'redo'): void
  (e: 'history'): void
  (e: 'exportToImage', imgtype: string): void
  (e: 'layoutGraph'): void
}>()

// 基础操作函数
const undo = () => {
  emit('undo')
  // 更新撤销重做状态
  updateUndoRedoState()
}

const redo = () => {
  emit('redo')
  // 更新撤销重做状态
  updateUndoRedoState()
}

const history = () => {
  emit('history')
}

// 添加注释节点
const addNote = () => {
  const viewport = getViewport()
  const noteNode = {
    id: `note_${nanoid()}`,
    type: 'note',
    position: {
      x: viewport.x + 100,
      y: viewport.y + 100
    },
    data: {
      title: '注释',
      content: '在此添加注释内容...',
      theme: 'yellow',
      showAuthor: false
    }
  }
  addNodes([noteNode])
  nodesStore.setNodes(getNodes.value)
}

// 导出图片功能
const exportToImage = async (imgtype: string) => {
  try {
    const workflowElement = document.querySelector('.vue-flow') as HTMLElement
    if (!workflowElement) {
      Message.error('未找到工作流画布')
      return
    }

    let dataUrl: string
    const options = {
      quality: 1,
      pixelRatio: 2,
      backgroundColor: '#ffffff'
    }

    switch (imgtype) {
      case 'png':
        dataUrl = await toPng(workflowElement, options)
        break
      case 'jpg':
      case 'jpeg':
        dataUrl = await toJpeg(workflowElement, options)
        break
      case 'svg':
        dataUrl = await toSvg(workflowElement)
        break
      default:
        dataUrl = await toPng(workflowElement, options)
    }

    // 下载图片
    const link = document.createElement('a')
    link.download = `workflow.${imgtype}`
    link.href = dataUrl
    link.click()

    Message.success(`工作流已导出为 ${imgtype.toUpperCase()} 格式`)
  } catch (error) {
    console.error('导出图片失败:', error)
    Message.error('导出图片失败')
  }
}

// 自动整理节点布局
const layoutGraph = () => {
  emit('layoutGraph')
}

// 缩放控制
const handleZoomIn = () => {
  zoomIn()
}

const handleZoomOut = () => {
  zoomOut()
}

const handleFitView = () => {
  fitView({ padding: 0.1 })
}

// 更新撤销重做状态
const updateUndoRedoState = () => {
  // 这里可以根据实际的历史记录状态来更新
  // 暂时设置为示例状态
  canUndo.value = true
  canRedo.value = true
}
</script>

<style scoped lang="scss">
.workflow-controls {
  .vue-flow__controls {
    position: absolute;
    left: 0;
    display: flex;
    gap: 2px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: white;
    padding: 8px;
    border: 1px solid var(--color-border-2);
  }

  .control-btn {
    color: var(--color-text-2);
    border: none;
    background: transparent;

    &:hover {
      color: var(--color-text-1);
      background-color: var(--color-fill-2);
    }

    &:disabled {
      color: var(--color-text-4);
      cursor: not-allowed;
    }
  }

  .control-divider {
    width: 100%;
    height: 1px;
    background-color: var(--color-border-2);
    margin: 4px 0;
  }
}

.node-list {
  display: flex;
  flex-direction: column;
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;

  .node-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4px;
    padding: 8px;
  }

  .node-list-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--color-fill-2);
      transform: translateY(-1px);
    }

    &-icon {
      margin-right: 12px;
      height: 24px;
      width: 24px;
      border-radius: 6px;
      background-color: var(--color-fill-3);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    &-text {
      font-size: 14px;
      color: var(--color-text-2);
      font-weight: 500;
    }
  }
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 8px;

  .tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--color-border-2);

    &:hover {
      background-color: var(--color-fill-2);
      border-color: var(--color-border-3);
      transform: translateY(-1px);
    }

    .tool-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 8px;
      color: var(--color-text-2);
    }

    .tool-name {
      font-size: 12px;
      color: var(--color-text-2);
      text-align: center;
    }
  }
}

.export-menu {
  min-width: 160px;
  padding: 4px;

  .export-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--color-fill-2);
    }

    .export-icon {
      margin-right: 8px;
      width: 16px;
      height: 16px;
      color: var(--color-text-3);
    }

    span {
      font-size: 14px;
      color: var(--color-text-2);
    }
  }
}
</style>
