import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { debounce } from 'lodash-es'
import type { Node, Edge } from '@vue-flow/core'

// 工作流历史事件类型 - 与 dify 保持一致
export enum WorkflowHistoryEvent {
  NodeTitleChange = 'NodeTitleChange',
  NodeDescriptionChange = 'NodeDescriptionChange',
  NodeDragStop = 'NodeDragStop',
  NodeChange = 'NodeChange',
  NodeConnect = 'NodeConnect',
  NodePaste = 'NodePaste',
  NodeDelete = 'NodeDelete',
  EdgeDelete = 'EdgeDelete',
  EdgeDeleteByDeleteBranch = 'EdgeDeleteByDeleteBranch',
  NodeAdd = 'NodeAdd',
  NodeResize = 'NodeResize',
  NoteAdd = 'NoteAdd',
  NoteChange = 'NoteChange',
  NoteDelete = 'NoteDelete',
  LayoutOrganize = 'LayoutOrganize'
}

// 历史状态接口
export interface WorkflowHistoryState {
  workflowHistoryEvent?: WorkflowHistoryEvent
  nodes: Node[]
  edges: Edge[]
  timestamp: number
}

// 变更历史条目接口
export interface ChangeHistoryEntry {
  index: number
  label?: string
  event?: WorkflowHistoryEvent
  stepCount: number
  isCurrent?: boolean
  state: WorkflowHistoryState
}

export const useWorkflowHistoryStore = defineStore('workflowHistory', () => {
  // 状态
  const pastStates = ref<WorkflowHistoryState[]>([])
  const futureStates = ref<WorkflowHistoryState[]>([])
  const currentState = ref<WorkflowHistoryState | null>(null)
  const maxHistorySize = ref(50)

  // 计算属性
  const canUndo = computed(() => pastStates.value.length > 0)
  const canRedo = computed(() => futureStates.value.length > 0)
  
  const currentHistoryIndex = computed(() => pastStates.value.length)
  
  const hasHistory = computed(() => pastStates.value.length > 0 || futureStates.value.length > 0)

  // 获取历史事件标签
  const getHistoryLabel = (event?: WorkflowHistoryEvent): string => {
    if (!event) return 'Unknown'
    
    const labels = {
      [WorkflowHistoryEvent.NodeAdd]: 'Add Node',
      [WorkflowHistoryEvent.NodeDelete]: 'Delete Node',
      [WorkflowHistoryEvent.NodeChange]: 'Edit Node',
      [WorkflowHistoryEvent.NodeDragStop]: 'Move Node',
      [WorkflowHistoryEvent.NodeConnect]: 'Connect Node',
      [WorkflowHistoryEvent.EdgeDelete]: 'Delete Edge',
      [WorkflowHistoryEvent.NoteAdd]: 'Add Note',
      [WorkflowHistoryEvent.NoteChange]: 'Edit Note',
      [WorkflowHistoryEvent.NoteDelete]: 'Delete Note',
      [WorkflowHistoryEvent.LayoutOrganize]: 'Organize Layout'
    }
    return labels[event] || 'Unknown'
  }

  // 计算变更历史列表 - 参考 dify 的实现
  const calculateChangeList = computed(() => {
    const result = {
      pastStates: [] as ChangeHistoryEntry[],
      futureStates: [] as ChangeHistoryEntry[]
    }

    // 处理过去状态
    pastStates.value.forEach((state, index) => {
      const stepCount = -(pastStates.value.length - index)
      result.pastStates.push({
        index: stepCount,
        label: getHistoryLabel(state.workflowHistoryEvent),
        event: state.workflowHistoryEvent,
        stepCount,
        state
      })
    })

    // 处理未来状态
    futureStates.value.forEach((state, index) => {
      const stepCount = index + 1
      result.futureStates.push({
        index: stepCount,
        label: getHistoryLabel(state.workflowHistoryEvent),
        event: state.workflowHistoryEvent,
        stepCount,
        state
      })
    })

    return result
  })

  // 保存状态到历史记录
  const saveStateToHistory = (state: WorkflowHistoryState) => {
    // 清理选中状态
    const cleanState: WorkflowHistoryState = {
      ...state,
      nodes: state.nodes.map(node => ({ ...node, selected: false })),
      edges: state.edges.map(edge => ({ ...edge, selected: false })),
      timestamp: Date.now()
    }

    // 如果当前状态存在，将其添加到过去状态
    if (currentState.value) {
      pastStates.value.push(currentState.value)
      
      // 限制历史记录大小
      if (pastStates.value.length > maxHistorySize.value) {
        pastStates.value.shift()
      }
    }

    // 设置新的当前状态
    currentState.value = cleanState

    // 清空未来状态（因为有了新的操作）
    futureStates.value = []
  }

  // 防抖保存状态 - 参考 dify 的实现
  const saveStateToHistoryDebounced = debounce((state: WorkflowHistoryState) => {
    saveStateToHistory(state)
  }, 500)

  // 撤销操作
  const undo = (steps = 1): WorkflowHistoryState | null => {
    if (pastStates.value.length === 0) return null

    let targetState: WorkflowHistoryState | null = null

    for (let i = 0; i < steps && pastStates.value.length > 0; i++) {
      // 将当前状态移到未来状态
      if (currentState.value) {
        futureStates.value.unshift(currentState.value)
      }

      // 从过去状态中取出最后一个状态
      targetState = pastStates.value.pop() || null
      if (targetState) {
        currentState.value = targetState
      }
    }

    return targetState
  }

  // 重做操作
  const redo = (steps = 1): WorkflowHistoryState | null => {
    if (futureStates.value.length === 0) return null

    let targetState: WorkflowHistoryState | null = null

    for (let i = 0; i < steps && futureStates.value.length > 0; i++) {
      // 将当前状态移到过去状态
      if (currentState.value) {
        pastStates.value.push(currentState.value)
      }

      // 从未来状态中取出第一个状态
      targetState = futureStates.value.shift() || null
      if (targetState) {
        currentState.value = targetState
      }
    }

    return targetState
  }

  // 跳转到指定状态 - 参考 dify 的实现
  const jumpToState = (entry: ChangeHistoryEntry): WorkflowHistoryState | null => {
    const diff = entry.index
    
    if (diff === 0) return currentState.value

    if (diff < 0) {
      return undo(Math.abs(diff))
    } else {
      return redo(diff)
    }
  }

  // 清空历史记录
  const clearHistory = () => {
    pastStates.value = []
    futureStates.value = []
    currentState.value = null
  }

  // 初始化状态
  const initializeState = (nodes: Node[], edges: Edge[]) => {
    const initialState: WorkflowHistoryState = {
      nodes: nodes.map(node => ({ ...node, selected: false })),
      edges: edges.map(edge => ({ ...edge, selected: false })),
      timestamp: Date.now()
    }
    
    currentState.value = initialState
  }

  // 获取当前状态
  const getCurrentState = () => currentState.value

  // 设置当前状态
  const setCurrentState = (state: WorkflowHistoryState) => {
    currentState.value = state
  }

  return {
    // 状态
    pastStates,
    futureStates,
    currentState,
    maxHistorySize,
    
    // 计算属性
    canUndo,
    canRedo,
    currentHistoryIndex,
    hasHistory,
    calculateChangeList,
    
    // 方法
    getHistoryLabel,
    saveStateToHistory,
    saveStateToHistoryDebounced,
    undo,
    redo,
    jumpToState,
    clearHistory,
    initializeState,
    getCurrentState,
    setCurrentState
  }
})

export type WorkflowHistoryStore = ReturnType<typeof useWorkflowHistoryStore>
