/*!
  * message-compiler v11.1.5
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
const LOCATION_STUB={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function createPosition(e,t,n){return{line:e,column:t,offset:n}}function createLocation(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const assign=Object.assign,isString=e=>"string"==typeof e;function join(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},COMPILE_ERROR_CODES_EXTEND_POINT=17,errorMessages={[CompileErrorCodes.EXPECTED_TOKEN]:"Expected token: '{0}'",[CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[CompileErrorCodes.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[CompileErrorCodes.EMPTY_PLACEHOLDER]:"Empty placeholder",[CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[CompileErrorCodes.INVALID_LINKED_FORMAT]:"Invalid linked format",[CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function createCompileError(e,t,n={}){const{domain:r,messages:o,args:c}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function defaultOnError(e){throw e}const RE_HTML_TAG=/<\/?[\w\s="/.':;#-\/]+>/,detectHtmlTag=e=>RE_HTML_TAG.test(e),CHAR_SP=" ",CHAR_CR="\r",CHAR_LF="\n",CHAR_LS=String.fromCharCode(8232),CHAR_PS=String.fromCharCode(8233);function createScanner(e){const t=e;let n=0,r=1,o=1,c=0;const s=e=>t[e]===CHAR_CR&&t[e+1]===CHAR_LF,i=e=>t[e]===CHAR_PS,a=e=>t[e]===CHAR_LS,u=e=>s(e)||(e=>t[e]===CHAR_LF)(e)||i(e)||a(e),E=e=>s(e)||i(e)||a(e)?CHAR_LF:t[e];function l(){return c=0,u(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>c,charAt:E,currentChar:()=>E(n),currentPeek:()=>E(n+c),next:l,peek:function(){return s(n+c)&&c++,c++,t[n+c]},reset:function(){n=0,r=1,o=1,c=0},resetPeek:function(e=0){c=e},skipToPeek:function(){const e=n+c;for(;e!==n;)l();c=0}}}const EOF=void 0,DOT=".",LITERAL_DELIMITER="'",ERROR_DOMAIN$1="tokenizer";function createTokenizer(e,t={}){const n=!1!==t.location,r=createScanner(e),o=()=>r.index(),c=()=>createPosition(r.line(),r.column(),r.index()),s=c(),i=o(),a={currentType:13,offset:i,startLoc:s,endLoc:s,lastType:13,lastOffset:i,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},u=()=>a,{onError:E}=t;function l(e,t,r){e.endLoc=c(),e.currentType=t;const o={type:t};return n&&(o.loc=createLocation(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const C=e=>l(e,13);function f(e,t){return e.currentChar()===t?(e.next(),t):(CompileErrorCodes.EXPECTED_TOKEN,c(),"")}function d(e){let t="";for(;e.currentPeek()===CHAR_SP||e.currentPeek()===CHAR_LF;)t+=e.currentPeek(),e.peek();return t}function p(e){const t=d(e);return e.skipToPeek(),t}function L(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function _(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=function(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function N(e){d(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function A(e,t=!0){const n=(t=!1,r="")=>{const o=e.currentPeek();return"{"===o?t:"@"!==o&&o?"|"===o?!(r===CHAR_SP||r===CHAR_LF):o===CHAR_SP?(e.peek(),n(!0,CHAR_SP)):o!==CHAR_LF||(e.peek(),n(!0,CHAR_LF)):t},r=n();return t&&e.resetPeek(),r}function T(e,t){const n=e.currentChar();return n===EOF?EOF:t(n)?(e.next(),n):null}function m(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function k(e){return T(e,m)}function I(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function S(e){return T(e,I)}function P(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function O(e){return T(e,P)}function h(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function R(e){return T(e,h)}function D(e){let t="",n="";for(;t=O(e);)n+=t;return n}function y(e){return e!==LITERAL_DELIMITER&&e!==CHAR_LF}function g(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return b(e,t,4);case"U":return b(e,t,6);default:return CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE,c(),""}}function b(e,t,n){f(e,t);let r="";for(let o=0;o<n;o++){const t=R(e);if(!t){CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE,c(),e.currentChar();break}r+=t}return`\\${t}${r}`}function U(e){return"{"!==e&&"}"!==e&&e!==CHAR_SP&&e!==CHAR_LF}function x(e){p(e);const t=f(e,"|");return p(e),t}function v(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER,c()),e.next(),n=l(t,2,"{"),p(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(CompileErrorCodes.EMPTY_PLACEHOLDER,c()),e.next(),n=l(t,3,"}"),t.braceNest--,t.braceNest>0&&p(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,c()),n=H(e,t)||C(t),t.braceNest=0,n;default:{let r=!0,o=!0,s=!0;if(N(e))return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,c()),n=l(t,1,x(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,c(),t.braceNest=0,M(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=L(e.currentPeek());return e.resetPeek(),r}(e,t))return n=l(t,4,function(e){p(e);let t="",n="";for(;t=S(e);)n+=t;return e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,c()),n}(e)),p(e),n;if(o=_(e,t))return n=l(t,5,function(e){p(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${D(e)}`):t+=D(e),e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,c()),t}(e)),p(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=e.currentPeek()===LITERAL_DELIMITER;return e.resetPeek(),r}(e,t))return n=l(t,6,function(e){p(e),f(e,"'");let t="",n="";for(;t=T(e,y);)n+="\\"===t?g(e):t;const r=e.currentChar();return r===CHAR_LF||r===EOF?(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,c(),r===CHAR_LF&&(e.next(),f(e,"'")),n):(f(e,"'"),n)}(e)),p(e),n;if(!r&&!o&&!s)return n=l(t,12,function(e){p(e);let t="",n="";for(;t=T(e,U);)n+=t;return n}(e)),CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER,c(),n.value,p(e),n;break}}return n}function H(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||o!==CHAR_LF&&o!==CHAR_SP||(CompileErrorCodes.INVALID_LINKED_FORMAT,c()),o){case"@":return e.next(),r=l(t,7,"@"),t.inLinked=!0,r;case".":return p(e),e.next(),l(t,8,".");case":":return p(e),e.next(),l(t,9,":");default:return N(e)?(r=l(t,1,x(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;d(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;d(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(p(e),H(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;d(e);const r=L(e.currentPeek());return e.resetPeek(),r}(e,t)?(p(e),l(t,11,function(e){let t="",n="";for(;t=k(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?L(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===CHAR_SP||!t)&&(t===CHAR_LF?(e.peek(),r()):A(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(p(e),"{"===o?v(e,t)||r:l(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===CHAR_SP?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(CompileErrorCodes.INVALID_LINKED_FORMAT,c()),t.braceNest=0,t.inLinked=!1,M(e,t))}}function M(e,t){let n={type:13};if(t.braceNest>0)return v(e,t)||C(t);if(t.inLinked)return H(e,t)||C(t);switch(e.currentChar()){case"{":return v(e,t)||C(t);case"}":return CompileErrorCodes.UNBALANCED_CLOSING_BRACE,c(),e.next(),l(t,3,"}");case"@":return H(e,t)||C(t);default:if(N(e))return n=l(t,1,x(e)),t.braceNest=0,t.inLinked=!1,n;if(A(e))return l(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===CHAR_SP||n===CHAR_LF)if(A(e))t+=n,e.next();else{if(N(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=a;return a.lastType=e,a.lastOffset=t,a.lastStartLoc=n,a.lastEndLoc=s,a.offset=o(),a.startLoc=c(),r.currentChar()===EOF?l(a,13):M(r,a)},currentOffset:o,currentPosition:c,context:u}}const ERROR_DOMAIN="parser",KNOWN_ESCAPES=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function fromEscapeSequence(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function createParser(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function o(e,n,r,o){t&&(e.end=n,e.loc&&(e.loc.end=r))}function c(e,t){const n=e.context(),c=r(3,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}function s(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,i=r(5,c,s);return i.index=parseInt(t,10),e.nextToken(),o(i,e.currentOffset(),e.currentPosition()),i}function i(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,i=r(4,c,s);return i.key=t,e.nextToken(),o(i,e.currentOffset(),e.currentPosition()),i}function a(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,i=r(9,c,s);return i.value=t.replace(KNOWN_ESCAPES,fromEscapeSequence),e.nextToken(),o(i,e.currentOffset(),e.currentPosition()),i}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let c=e.nextToken();if(8===c.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:c,lastStartLoc:s}=n,i=r(8,c,s);return 11!==t.type?(CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,i.value="",o(i,c,s),{nextConsumeToken:t,node:i}):(null==t.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,getTokenCaption(t)),i.value=t.value||"",o(i,e.currentOffset(),e.currentPosition()),{node:i})}(e);n.modifier=t.node,c=t.nextConsumeToken||e.nextToken()}switch(9!==c.type&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(c)),c=e.nextToken(),2===c.type&&(c=e.nextToken()),c.type){case 10:null==c.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(c)),n.key=function(e,t){const n=e.context(),c=r(7,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}(e,c.value||"");break;case 4:null==c.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(c)),n.key=i(e,c.value||"");break;case 5:null==c.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(c)),n.key=s(e,c.value||"");break;case 6:null==c.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(c)),n.key=a(e,c.value||"");break;default:{CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),i=r(7,s.offset,s.startLoc);return i.value="",o(i,s.offset,s.startLoc),n.key=i,o(n,s.offset,s.startLoc),{nextConsumeToken:c,node:n}}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function E(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let E=null;do{const r=E||e.nextToken();switch(E=null,r.type){case 0:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(c(e,r.value||""));break;case 5:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(s(e,r.value||""));break;case 4:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(i(e,r.value||""));break;case 6:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.items.push(a(e,r.value||""));break;case 7:{const t=u(e);n.items.push(t.node),E=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function l(e){const t=e.context(),{offset:n,startLoc:c}=t,s=E(e);return 13===t.currentType?s:function(e,t,n,c){const s=e.context();let i=0===c.items.length;const a=r(1,t,n);a.cases=[],a.cases.push(c);do{const t=E(e);i||(i=0===t.items.length),a.cases.push(t)}while(13!==s.currentType);return i&&CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL,o(a,e.currentOffset(),e.currentPosition()),a}(e,n,c,s)}return{parse:function(n){const c=createTokenizer(n,assign({},e)),s=c.context(),i=r(0,s.offset,s.startLoc);return t&&i.loc&&(i.loc.source=n),i.body=l(c),e.onCacheKey&&(i.cacheKey=e.onCacheKey(n)),13!==s.currentType&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),o(i,c.currentOffset(),c.currentPosition()),i}}}function getTokenCaption(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function createTransformer(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}function traverseNodes(e,t){for(let n=0;n<e.length;n++)traverseNode(e[n],t)}function traverseNode(e,t){switch(e.type){case 1:traverseNodes(e.cases,t),t.helper("plural");break;case 2:traverseNodes(e.items,t);break;case 6:traverseNode(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function transform(e,t={}){const n=createTransformer(e);n.helper("normalize"),e.body&&traverseNode(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function optimize(e){const t=e.body;return 2===t.type?optimizeMessageNode(t):t.cases.forEach((e=>optimizeMessageNode(e))),e}function optimizeMessageNode(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=join(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function minify(e){switch(e.t=e.type,e.type){case 0:{const t=e;minify(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)minify(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)minify(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;minify(t.key),t.k=t.key,delete t.key,t.modifier&&(minify(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function createCodeGenerator(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:c}=t,s=!1!==t.location,i={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:c,indentLevel:0};s&&e.loc&&(i.source=e.loc.source);function a(e,t){i.code+=e}function u(e,t=!0){const n=t?o:"";a(c?n+"  ".repeat(e):n)}return{context:()=>i,push:a,indent:function(e=!0){const t=++i.indentLevel;e&&u(t)},deindent:function(e=!0){const t=--i.indentLevel;e&&u(t)},newline:function(){u(i.indentLevel)},helper:e=>`_${e}`,needIndent:()=>i.needIndent}}function generateLinkedNode(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),generateNode(e,t.key),t.modifier?(e.push(", "),generateNode(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function generateMessageNode(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let c=0;c<o&&(generateNode(e,t.items[c]),c!==o-1);c++)e.push(", ");e.deindent(r()),e.push("])")}function generatePluralNode(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(generateNode(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}function generateResource(e,t){t.body?generateNode(e,t.body):e.push("null")}function generateNode(e,t){const{helper:n}=e;switch(t.type){case 0:generateResource(e,t);break;case 1:generatePluralNode(e,t);break;case 2:generateMessageNode(e,t);break;case 6:generateLinkedNode(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const generate=(e,t={})=>{const n=isString(t.mode)?t.mode:"normal",r=isString(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,c=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,i=e.helpers||[],a=createCodeGenerator(e,{mode:n,filename:r,sourceMap:o,breakLineCode:c,needIndent:s});a.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(s),i.length>0&&(a.push(`const { ${join(i.map((e=>`${e}: _${e}`)),", ")} } = ctx`),a.newline()),a.push("return "),generateNode(a,e),a.deindent(s),a.push("}"),delete e.helpers;const{code:u,map:E}=a.context();return{ast:e,code:u,map:E?E.toJSON():void 0}};function baseCompile(e,t={}){const n=assign({},t),r=!!n.jit,o=!!n.minify,c=null==n.optimize||n.optimize,s=createParser(n).parse(e);return r?(c&&optimize(s),o&&minify(s),{ast:s,code:""}):(transform(s,n),generate(s,n))}export{COMPILE_ERROR_CODES_EXTEND_POINT,CompileErrorCodes,ERROR_DOMAIN,LOCATION_STUB,baseCompile,createCompileError,createLocation,createParser,createPosition,defaultOnError,detectHtmlTag,errorMessages};
